{"name": "smart-cms-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-jsapi-types": "^0.0.15", "@ant-design/icons": "^5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@icon-park/react": "^1.4.2", "@tailwindcss/vite": "^4.0.7", "@vis.gl/react-google-maps": "^1.5.0", "antd": "^5.24.3", "antd-style": "^3.7.1", "axios": "^1.7.9", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "jotai": "^2.12.1", "localforage": "^1.10.0", "lucide-react": "^0.503.0", "normalize.css": "^8.0.1", "qs": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-router-dom": "^7.5.2", "tailwindcss": "^4.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.19.0", "@types/node": "^22.13.4", "@types/qs": "^6.9.18", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "less": "^4.2.2", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@swc/core", "core-js", "esbuild"]}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}