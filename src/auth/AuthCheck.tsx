import {useAtom} from "jotai";
import {userAuth<PERSON>tom} from "@/jotai/atoms.ts";

interface AuthButtonProps {
  requiredPerm: string;
  children: React.ReactElement;
  fallback?: React.ReactNode;
}
const AuthCheck = ({ requiredPerm, children, fallback = null }: AuthButtonProps) => {
  const [auth] = useAtom(userAuthAtom);
  // 开发环境直接放行
  if (import.meta.env.DEV) {
    return children;
  }
  // 获取解密后的权限
  const hasPermission = auth?.permissions?.includes(requiredPerm);
  const isAdmin = auth?.userInfo?.role === "admin";

  return isAdmin || hasPermission ? children : fallback;
};

export default AuthCheck;
