// src/i18n.ts
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LngDetector from 'i18next-browser-languagedetector';
import zh_CN from "./zh_CN.json";
import en from "./en.json";

const resources = {
  "zh-CN": {
    translation: zh_CN,
  },
  en: {
    translation: en,
  },
  // 其他语言...
};
i18n
  .use(LngDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "zh-CN",
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  })
  .then(() => {
    console.log("i18n initialized", i18n.language);
  })
  .catch(() => {
    console.error("i18n init failed");
  });

export default i18n;
