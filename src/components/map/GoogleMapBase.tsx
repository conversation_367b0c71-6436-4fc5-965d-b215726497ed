// biome-ignore lint/suspicious/noShadowRestrictedNames: <explanation>
import {APIProvider, Map, Marker} from '@vis.gl/react-google-maps';
import { useState } from 'react';
import ld from './ld.png';

interface Location {
    lat: number;
    lng: number;
    title?: string;
    icon?: string;
    description?: string;
}

interface InfoWindowProps {
    position: google.maps.LatLngLiteral;
    onClose: () => void;
    location: Location;
}

function InfoWindow({ position, onClose, location }: Readonly<InfoWindowProps>) {
    return (
        <div
            style={{
                position: 'absolute',
                // biome-ignore lint/style/noUnusedTemplateLiteral: <explanation>
                transform: `translate(-50%, -100%)`,
                left: '50%',
                top: '50%',
            }}
            className="min-w-[200px] bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 animate-fade-in"
        >
            <div className="flex justify-between text-gray-900 dark:text-gray-200">
                <h4 className="font-medium">{location.title || '位置信息'}</h4>
                {/* biome-ignore lint/a11y/useButtonType: <explanation> */}
                <button
                    onClick={onClose}
                    className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                >
                    {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
                    <svg viewBox="0 0 1024 1024" width="14" height="14">
                        <path
                            className="fill-gray-800 dark:fill-gray-100"
                            d="M886.784 746.496q29.696 30.72 43.52 56.32t-4.608 58.368q-4.096 6.144-11.264 14.848t-14.848 16.896-15.36 14.848-12.8 9.728q-25.6 15.36-60.416 8.192t-62.464-34.816l-43.008-43.008-57.344-57.344-67.584-67.584-73.728-73.728-131.072 131.072q-60.416 60.416-98.304 99.328-38.912 38.912-77.312 48.128t-68.096-17.408l-7.168-7.168-11.264-11.264-11.264-11.264q-6.144-6.144-7.168-8.192-11.264-14.336-13.312-29.184t2.56-29.184 13.824-27.648 20.48-24.576q9.216-8.192 32.768-30.72l55.296-57.344q33.792-32.768 75.264-73.728t86.528-86.016q-49.152-49.152-93.696-93.184t-79.872-78.848-57.856-56.832-27.648-27.136q-26.624-26.624-27.136-52.736t17.92-52.736q8.192-10.24 23.552-24.064t21.504-17.92q30.72-20.48 55.296-17.92t49.152 28.16l31.744 31.744q23.552 23.552 58.368 57.344t78.336 76.288 90.624 88.576q38.912-38.912 76.288-75.776t69.632-69.12 58.368-57.856 43.52-43.008q24.576-23.552 53.248-31.232t55.296 12.8q1.024 1.024 6.656 5.12t11.264 9.216 10.752 9.728 7.168 5.632q27.648 26.624 27.136 57.856t-27.136 57.856q-18.432 18.432-45.568 46.08t-60.416 60.416-70.144 69.632l-77.824 77.824q37.888 36.864 74.24 72.192t67.584 66.048 56.32 56.32 41.472 41.984z"
                        />
                    </svg>
                </button>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mt-2">{location.description || '暂无描述'}</p>
        </div>
    );
}

interface PoiMarkersProps {
    pois: Location[];
    onMarkerClick: (location: Location) => void;
}

function PoiMarkers({ pois, onMarkerClick }: Readonly<PoiMarkersProps>) {
    return (
        <>
            {pois.map((poi, index) => (
                <Marker
                    // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                    key={index}
                    position={{ lat: poi.lat, lng: poi.lng }}
                    title={poi.title}
                    icon={{
                        url: poi.icon || ld,
                        scaledSize: new google.maps.Size(32, 32),
                        anchor: new google.maps.Point(16, 32),
                    }}
                    onClick={() => onMarkerClick(poi)}
                />
            ))}
        </>
    );
}

interface GoogleMapProps {
    locations?: Location[];
}

import {useAtom} from "jotai/index";
import {themeAtom} from "@/jotai/atoms.ts";

export default function GoogleMapBase({ locations = [] }: Readonly<GoogleMapProps>) {
    const apiKey = import.meta.env.VITE_BASE_GOOGLE_MAP_KEY;
    const [mapError, setMapError] = useState<string>('');
    const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
    const [theme] = useAtom(themeAtom);
    

    if (!apiKey) {
        return <div className="w-full h-full flex items-center justify-center text-red-500">
            Google Maps API key is not configured
        </div>;
    }

    const handleError = (error: unknown) => {
        console.error('Google Maps API error:', error);
        if (error && typeof error === 'object' && 'message' in error) {
            setMapError(error.message as string);
        } else {
            setMapError('An error occurred while loading Google Maps');
        }
    };

    const handleMarkerClick = (location: Location) => {
        setSelectedLocation(location);
    };

    return (
        <APIProvider 
            apiKey={apiKey}
            onError={handleError}
        >
            {mapError ? (
                <div className="w-full h-full flex items-center justify-center text-red-500">
                    {mapError}
                </div>
            ) : (
                <Map
                    style={{width: '100%', height: '100%'}}
                    defaultCenter={{lat: 22.54992, lng: 113.94924}} 
                    defaultZoom={11}
                    gestureHandling={'greedy'}
                    disableDefaultUI={false}
                    colorScheme={theme === 'dark' ? 'DARK' : 'LIGHT'}
                    mapTypeControl={true}
                    streetViewControl={true}
                    scaleControl={true}
                    fullscreenControl={true}
                    zoomControl={true}
                >
                    <PoiMarkers pois={locations} onMarkerClick={handleMarkerClick} />
                    {selectedLocation && (
                        <InfoWindow
                            position={{ lat: selectedLocation.lat, lng: selectedLocation.lng }}
                            onClose={() => setSelectedLocation(null)}
                            location={selectedLocation}
                        />
                    )}
                </Map>
            )}
        </APIProvider>
    );
}