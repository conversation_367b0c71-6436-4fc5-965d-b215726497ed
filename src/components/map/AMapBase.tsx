import {themeAtom} from "@/jotai/atoms.ts";
import AMapLoader from '@amap/amap-jsapi-loader';
import {useAtom} from "jotai/index";
import {useEffect, useRef} from 'react';
import "@amap/amap-jsapi-types";
export default function AMapBase() {
    const [theme] = useAtom(themeAtom);
    const mapContainerRef = useRef<HTMLDivElement>(null);
    const mapRef = useRef<AMap.Map | null>(null);
    const aMapRef = useRef<any | null>(null);
    const infoWindowRef = useRef<AMap.InfoWindow | null>(null);
    const animationRef = useRef<number | undefined>(undefined);
    useEffect(() => {

        const initMap = async () => {
            try {
                
                // @ts-ignore
                window._AMapSecurityConfig = {
                    securityJsCode: "f10b60d86355f09ab12429288fa3ab0b",
                };
                // @ts-ignore
        
                const AMap = await AMapLoader.load({
                    key: "874f2113f85ae1f01c38af2f8dd3c0cf",
                    version: "2.0",
                    plugins: ["AMap.Scale", "AMap.ElasticMarker", "AMap.ToolBar", "AMap.HawkEye", "AMap.MapType"],
                });

                aMapRef.current = AMap;
                
                mapRef.current = new AMap.Map("AMap", {
                    mapStyle: theme === "dark" ? "amap://styles/grey" : "amap://styles/normal",
                    viewMode: "2D",
                    zoom: 11,
                    center: [116.397428, 39.90923],
                });

                loadToolsMap();
                handelClick();
            } catch (error) {
                console.warn("Failed to initialize AMap:", error);
            }
        };
        // 刷新偶尔会一次加载不出来，所以加载两次
        initMap();
        initMap();

        return () => {
            // 清除AMap的缓存
            const amapKeys = Object.keys(localStorage).filter(key => key.match(/^_AMap_/))
            // biome-ignore lint/complexity/noForEach: <explanation>
            amapKeys.forEach(key => {
                // console.log(key)
                localStorage.removeItem(key)
              });
            if (infoWindowRef.current) {
                infoWindowRef.current.close();
                infoWindowRef.current = null;
            }
            if (mapRef.current) {
                mapRef.current.destroy();
                mapRef.current = null;
            }
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
                animationRef.current = undefined;
            }
            aMapRef.current = null;
        };
    }, [theme]);

    const loadToolsMap = () => {
        if (mapRef.current && aMapRef.current) {
            //添加工具条控件，工具条控件集成了缩放、平移、定位等功能按钮在内的组合控件
            mapRef.current.addControl(new aMapRef.current.ToolBar());
            //添加比例尺控件，展示地图在当前层级和纬度下的比例尺
            mapRef.current.addControl(new aMapRef.current.Scale());
            //添加类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
            mapRef.current.addControl(new aMapRef.current.MapType());
        }
     }
    const handelClick = () => {
        const originPos = new aMapRef.current.LngLat(116.39, 39.9);
        const marker: AMap.Marker = new aMapRef.current.Marker({
            position: new aMapRef.current.LngLat(116.39, 39.9),
            title: "北京",
            extData: {  // 新增自定义数据字段
                id: 1,
                name: "北京地标",
                description: "中华人民共和国首都"
            }
        });
        //将创建的点标记添加到已有的地图实例：
        mapRef.current?.add(marker);

        // 添加跳动动画
        // biome-ignore lint/style/useConst: <explanation>
        let startTime = Date.now();
        const animate = () => {
            if (!aMapRef.current || !marker || !originPos) {
                if (animationRef.current) {
                    cancelAnimationFrame(animationRef.current);
                    animationRef.current = undefined;
                }
                return;
            }
            const time = Date.now() - startTime;
            const offsetY = Math.sin(time * 0.005) * 0.002; // 调整0.0002改变跳动幅度
            marker.setPosition(new aMapRef.current.LngLat(
                originPos.getLng(),
                originPos.getLat() + offsetY
            ));
            animationRef.current = requestAnimationFrame(animate);
        };
        animate();
        // 清理动画
        marker.on("click", (e) => {

            const contentEl = document.createElement('div');
            contentEl.innerHTML = `
                <div style="min-width:200px;" class="bg-white dark:bg-gray-800 rounded p-4">
                    <div class="flex justify-between text-gray-900 dark:text-gray-200">
                        <h4>${e.target.getExtData().name}</h4>
                        <button class="close-btn text-gray-500 dark:text-gray-400">
                            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                                  width="14" height="14">
                                <path
                                    class= "fill-gray-800 dark:fill-gray-100"
                                    d="M886.784 746.496q29.696 30.72 43.52 56.32t-4.608 58.368q-4.096 6.144-11.264 14.848t-14.848 16.896-15.36 14.848-12.8 9.728q-25.6 15.36-60.416 8.192t-62.464-34.816l-43.008-43.008-57.344-57.344-67.584-67.584-73.728-73.728-131.072 131.072q-60.416 60.416-98.304 99.328-38.912 38.912-77.312 48.128t-68.096-17.408l-7.168-7.168-11.264-11.264-11.264-11.264q-6.144-6.144-7.168-8.192-11.264-14.336-13.312-29.184t2.56-29.184 13.824-27.648 20.48-24.576q9.216-8.192 32.768-30.72l55.296-57.344q33.792-32.768 75.264-73.728t86.528-86.016q-49.152-49.152-93.696-93.184t-79.872-78.848-57.856-56.832-27.648-27.136q-26.624-26.624-27.136-52.736t17.92-52.736q8.192-10.24 23.552-24.064t21.504-17.92q30.72-20.48 55.296-17.92t49.152 28.16l31.744 31.744q23.552 23.552 58.368 57.344t78.336 76.288 90.624 88.576q38.912-38.912 76.288-75.776t69.632-69.12 58.368-57.856 43.52-43.008q24.576-23.552 53.248-31.232t55.296 12.8q1.024 1.024 6.656 5.12t11.264 9.216 10.752 9.728 7.168 5.632q27.648 26.624 27.136 57.856t-27.136 57.856q-18.432 18.432-45.568 46.08t-60.416 60.416-70.144 69.632l-77.824 77.824q37.888 36.864 74.24 72.192t67.584 66.048 56.32 56.32 41.472 41.984z"
                                   ></path>
                            </svg>
                        </button>
                    </div>
                    <p>ID: ${e.target.getExtData().id}</p>
                    <p>${e.target.getExtData().description}</p>
                </div>
            `;
            const infoWindow = new aMapRef.current.InfoWindow({
                isCustom: true,
                autoMove: true,
                closeWhenClickMap: true,
                content: contentEl,  // 使用自定义数据构建信息框
                offset: new aMapRef.current.Pixel(0, -30) // 调整信息框位置
            });

            infoWindow.open(mapRef.current, e.target.getPosition());
            // 绑定关闭事件
            const closeButton = contentEl.querySelector('.close-btn');
            closeButton?.addEventListener('click', () => {
                infoWindow.close();
            });
        });
    }
    return (
        <div  ref={mapContainerRef} className='w-full h-full' id="AMap">

        </div>
    );
}