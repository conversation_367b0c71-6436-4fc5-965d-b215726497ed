import DeviceManagementIcons from "@/assets/svgIcons/DeviceManagement";
import FaultIcons from "@/assets/svgIcons/FaultIcons";
import type { Color } from "@/assets/svgIcons/IconType";
import LanguageIcons from "@/assets/svgIcons/LanguageIcons";
import MapIcons from "@/assets/svgIcons/MapIcons";
import NoticeIcons from "@/assets/svgIcons/NoticeIcons";
import ReportIcon from "@/assets/svgIcons/ReportIcon";
import SystemIcons from "@/assets/svgIcons/SystemIcons";
import logo from "@/assets/yay.jpg";
import AuthCheck from "@/auth/AuthCheck";
import type { LanguageType, PageType } from "@/components/type";
import { themeAtom } from "@/jotai/atoms";
import { ControlOutlined, FileSearchOutlined, LoginOutlined, UserOutlined, UserSwitchOutlined } from "@ant-design/icons";
import { Avatar, Badge, Divider, Popover } from "antd";
import classNames from "classnames";
import { useAtom } from "jotai";
import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import styles from "./component.module.less";
import UserManagement from "@/assets/svgIcons/UserManagement";

// 类型定义

type NavItemConfig = {
  page: PageType;
  perm: string;
  icon: React.ComponentType<{ color?: Color; width?: number; height?: number }>; // 修改icon类型定义
  text: string;
  router: string;
};

export default function HeaderTop() {
  const { t, i18n } = useTranslation();
  const [hoverIndex, setHoverIndex] = useState<number>(-1);
  const [focusedPage, setFocusedPage] = useState<PageType>("system");
  const [theme, setTheme] = useAtom(themeAtom);
    
  // 使用从useNavigate获取的navigate函数进行路由跳转
  const navigate = useNavigate();
  // 设置主题
  const toggleTheme = () => {
    // 立即切换主题
    requestAnimationFrame(() => {
      setTheme((prev) => (prev === "light" ? "dark" : "light"));
    });
    
  };

  useEffect(() => {
    console.log("location", location);
  }, []);
  const focusedColor = theme === "dark" ? "#fff" : "#111827";
  const unFocusedColor = theme === "dark" ? "#9ca3af" : "#fff";
  const NAV_ITEMS: NavItemConfig[] = [
    {
      router: 'system/overview',
      page: 'system',
      perm: 'system',
      icon: SystemIcons,
      text: 'system_overview',
    },
    {
      router: 'equipment/overview',
      page: 'equipment',
      perm: 'equipment',
      icon: DeviceManagementIcons,
      text: 'equipment',
    },
    {
      router: 'map_preview',
      page: 'map',
      perm: 'map',
      icon: MapIcons,
      text: 'map_view',
    },
    {
      router: '/',
      page: 'report',
      perm: 'report',
      icon: ReportIcon,
      text: 'report_statistics',
    },
    {
      router: '/',
      page: 'fault',
      perm: 'fault',
      icon: FaultIcons,
      text: 'fault_management',
    },
    {
      router: 'manage',
      page: 'user',
      perm: 'user',
      icon: UserManagement,
      text: 'system_management',
    },
  ];
  const pages = NAV_ITEMS.map((item) => item.page);

  const itemRefs = useRef<(HTMLButtonElement | HTMLDivElement | null)[]>([]);
  // 下划线样式
  const [mainUnderline, setMainUnderline] = useState({ left: 0, width: 0 });
  const [hoverUnderline, setHoverUnderline] = useState({ left: 0, width: 0 });

  // 更新下划线位置的方法（拆分为两个独立方法）
  const updateMainUnderline = useCallback((index: number) => {
    const target = itemRefs.current[index];
    const container = target?.parentElement;
    if (target && container) {
      const containerRect = container.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      setMainUnderline({
        left: targetRect.left - containerRect.left,
        width: targetRect.width,
      });
    }
  }, []);
  const updateHoverUnderline = useCallback((index: number) => {
    const target = itemRefs.current[index];
    const container = target?.parentElement;
    if (target && container) {
      const containerRect = container.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      setHoverUnderline({
        left: targetRect.left - containerRect.left,
        width: targetRect.width,
      });
    }
  }, []);
  // 处理页面点击
  const handlePageChange = (page: PageType, router:string) => {
    const index = NAV_ITEMS.findIndex((item) => item.page === page);
    if (index !== -1) {
      setFocusedPage(page);
      updateMainUnderline(index);
      
      navigate(router);
    }
  };
  // 处理悬停事件
  const handleHover = (index: number) => {
    if (index === -1 || index === NAV_ITEMS.findIndex((i) => i.page === focusedPage)) {
      setHoverIndex(-1);
      return;
    }
    setHoverIndex(index);
    updateHoverUnderline(index);
  };
  // 添加 resize 监听
    useEffect(() => {
    const handleResize = () => {
      const activeIndex = pages.indexOf(focusedPage);
      if (activeIndex !== -1) updateMainUnderline(activeIndex);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [focusedPage, updateMainUnderline]);

  // Initialize underline position
    useEffect(() => {
    const initialIndex = pages.indexOf(focusedPage);
    if (initialIndex !== -1) updateMainUnderline(initialIndex);
  }, [i18n.language, updateMainUnderline]);

  // switch language
  interface LANG_ITEM {
    language: LanguageType;
    name: string;
  }
  const LANG_LIST: LANG_ITEM[] = [
    { language: "en", name: "English" },
    { language: "zh-CN", name: "简体中文" },
    { language: "es", name: "Español" },
    { language: "ro", name: "Română" },
  ];
  const handelChangeLanguage = (language: LanguageType) => {
    i18n.changeLanguage(language).then(() => {
      localStorage.setItem("language", language);
      const activeIndex = pages.indexOf(focusedPage);
      if (activeIndex !== -1) {
        updateMainUnderline(activeIndex);
      }
    });
  };

  const ContentLanguage = (
    <div className={"flex flex-col justify-center items-center  cursor-default"}>
      {LANG_LIST.map((item) => (
          <button
          key={item.language}
          type="button"
          onClick={() => {
            handelChangeLanguage(item.language);
          }}
          className={classNames(
            i18n.resolvedLanguage === item.language ? "font-bold text-gray-900 dark:!text-white" : "",
            "pl-2 w-[100px] h-[30px] text-md leading-[30px] text-center hover:bg-gray-200 hover:dark:bg-gray-600 rounded dark:text-gray-400",
          )}
        >
          {item.name}
        </button>
      ))}
    </div>
  );

  // 故障
  const [notice, setNotice] = useState<number>(0);
  useEffect(() => {
    setNotice(9)
  }, []);
  const handelNotice = () => {
    console.log("=>(HeaderTop.tsx:157) ", notice);
  };
  const userList = [
    {
      title: t("my_info"),
      router: "",
      Icon: <UserOutlined />,
    },
    {
      title: t("project_management"),
      router: "",
      Icon: <ControlOutlined />,
    },
    {
      title: t("sub_account_management"),
      router: "",
      Icon: <UserSwitchOutlined />,
    },
    {
      title: t("log"),
      router: "",
      Icon: <FileSearchOutlined />,
    },
    {
      title: t("log_out"),
      router: "",
      Icon: <LoginOutlined />,
    },
  ];
  const handelPushRouter = (router: string) => {
    console.log("=>(HeaderTop.tsx:187) ", router);
  };
  const ContextUser = (
    <div className={"flex w-[200px] flex-col justify-center items-center  cursor-default"}>
      <Avatar size={30} icon={<UserOutlined />} />
      <div className={"font-bold"}>Admin</div>
      <div><EMAIL></div>
      <Divider className={"my-3"} />
      {userList.map((item, index) => {
        return (
          <button
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            key={index}
            type="button"
            onClick={() => {
              handelPushRouter(item.router);
            }}
            className={classNames("pl-2 w-full h-[30px] text-md flex justify-start items-center hover:bg-gray-200 hover:dark:bg-gray-600 rounded dark:text-gray-400")}
          >
            {item.Icon}
            <div className={"ml-2"}>{item.title}</div>
          </button>
        );
      })}
    </div>
  );
  return (
      <div className={"w-full box-border pl-2 pr-6 h-14 flex justify-between items-center bg-elite shadow-lg dark:bg-gray-800 "}>
        <div className={"flex flex-1 overflow-auto justify-start items-center cursor-default"}>
          <div className={"w-[240px] h-12 flex items-center justify-center"}>
            <img src={logo} alt={"logo"} className={"h-12 bg-no-repeat bg-contain "} />
          </div>
          <Divider type="vertical" className={"!h-5 !w-1 !border-gray-300"} />
          <div className={"flex relative items-center text-white dark:text-gray-400 "}>
            {/* 悬停下划线（独立动画） */}
            <div
              className={classNames(
                styles.hover_underline,
                "absolute bottom-[-1px] h-0.5 bg-gray-800 dark:bg-gray-300",
                "transform origin-center transition-all duration-300 ease-in-out",
                hoverIndex === -1 ? "scale-x-0 opacity-0" : "scale-x-100 opacity-90",
              )}
              style={{
                left: hoverUnderline.left,
                width: hoverUnderline.width,
                transition: "all 0.3s ease-in-out"
              }}
            />

            {/* 主下划线（点击固定） */}
            <div
              className={classNames(
                styles.dynamic_underline,
                "absolute bottom-[-1px] h-0.5 bg-gray-900 dark:bg-gray-300",
                "transition-all duration-300 ease-in-out"
              )}
              style={{
                left: mainUnderline.left,
                width: mainUnderline.width,
                transition: "all 0.3s ease-in-out"
              }}
            />

            {NAV_ITEMS.map(({ page, perm, icon: Icon, text, router }, index) => (
              <AuthCheck key={page} requiredPerm={perm}>
                <button
                  type="button"
                  ref={(el) => {
                    itemRefs.current[index] = el;
                  }}
                  className={classNames("mx-4 flex h-[40px] items-center cursor-pointer", focusedPage === page ? "text-gray-900 dark:text-white" : "")}
                  onClick={() => handlePageChange(page, router)}
                  onMouseEnter={() => handleHover(index)}
                  onMouseLeave={() => handleHover(-1)}
                >
                  <Icon color={focusedPage === page ? focusedColor : unFocusedColor} />
                  <div className="pl-1">{t(text)}</div>
                </button>
              </AuthCheck>
            ))}
          </div>
        </div>
        <div className={"flex items-center cursor-default"}>
          <Badge
            count={notice}
            size={"small"}
            offset={[notice > 10 ? -15 : -5, 10]}
            onClick={() => {
              handelNotice();
            }}
            overflowCount={10}
          >
            <NoticeIcons width={30} height={30} />
          </Badge>
          <Divider className={"!h-3 !border-gray-300 mx-3"} type={"vertical"} />
          <Popover placement="bottomRight" content={ContentLanguage} arrow={false} forceRender={true}>
            <div className={"bg-transparent border-none hover:bg-transparent"}>
              <LanguageIcons width={30} height={30} />
            </div>
          </Popover>
          <Divider className={"!h-3 !border-gray-300 mx-3"} type={"vertical"} />
          <Popover placement="bottomRight" content={ContextUser} arrow={false} forceRender={true}>
            <Avatar size={30} icon={<UserOutlined />} />
          </Popover>
          <Divider className={"!h-3 !border-gray-300 mx-3"} type={"vertical"} />
          <div className={classNames(styles.theme_switch)}>
            <label htmlFor="switch" className={styles.switch} aria-label={t("toggle_theme")}>
              <input
                id="switch"
                type="checkbox"
                checked={theme === "light"}
                onChange={(e) => {
                  e.stopPropagation();
                  toggleTheme();
                }}
              />
              <span className={styles.slider} />
              <span className={styles.decoration} />
            </label>
          </div>
          
        </div>
      </div>
  );
}
