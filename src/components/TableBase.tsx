import {Divider, Pagination, Table} from "antd";
import type { TableProps } from 'antd';
import { useRef, useEffect, useState } from 'react';
import { createStyles } from 'antd-style';
import { useTranslation } from 'react-i18next';
import type { TablePropsType } from './type';
import classNames from "classnames";

const useStyle = createStyles(({ css }) => {
  return {
    customTable: css`
      .ant-table {
        .ant-table-container {
          .ant-table-body {
            scrollbar-width: thin;
            scrollbar-gutter: stable;
            transition: none !important;
            min-height: var(--table-body-height) !important;
            overflow-y: auto !important;
          }
        }
      }
    `,
  };
});

export default function TableBase<T extends { key: string }>({
    columns,
    dataSource,
    className,
    total,
    callbackPages,
    tableProps,
}: Readonly<TablePropsType<T>>) {
    const { styles } = useStyle();
    const { t } = useTranslation();
    const containerRef = useRef<HTMLDivElement>(null);
    const [scrollY, setScrollY] = useState<number>(0);

    const finalTableProps: TableProps<T> = {
        ...tableProps,
    };


    useEffect(() => {
        if (containerRef.current) {
            const updateScrollY = () => {
                const height = containerRef.current?.clientHeight || 0;
                // 减去分页器和其他边距的高度
                const tableBodyHeight = height - 95;
                setScrollY(tableBodyHeight);
                // 设置CSS变量用于最小高度
                containerRef.current?.style.setProperty('--table-body-height', `${tableBodyHeight}px`);
            };

            updateScrollY();
            
        }
    }, []);


    return (
        <div ref={containerRef} className={`w-full h-full border border-gray-200 dark:border-gray-500 rounded-lg overflow-hidden ${className || ''}`}>
            <Table<T>
                {...finalTableProps}
                className={classNames(styles.customTable, )}
                pagination={false}
                columns={columns}
                dataSource={dataSource}
                scroll={{
                    x: "max-content",
                    y: scrollY
                }}
            />
            <Divider className="!m-0"/>
            <div className="flex justify-end my-1 px-3 overflow-hidden">
                <Pagination
                    total={total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => t('antd.pagination.total', { total })}
                    onChange={(page, pageSize) => callbackPages(page, pageSize)}
                />
            </div>
        </div>
    )
}