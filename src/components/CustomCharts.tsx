import { useLayoutEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { ChartsPropsType } from './type';

function CustomCharts({ option, customClassName }: Readonly<ChartsPropsType>) {
    const chartsNode = useRef(null);
    const chartsInstance = useRef<echarts.ECharts | null>(null);

    useLayoutEffect(() => {
        // 初始化图表
        chartsInstance.current = echarts.init(chartsNode.current);
        chartsInstance.current?.setOption(option);

        // 监听窗口尺寸变化
        const handleResize = () => {
            chartsInstance.current?.resize();
        };
        window.addEventListener('resize', handleResize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            chartsInstance.current?.dispose();
        };
    }, [option]);

    // 更新图表配置
    useLayoutEffect(() => {
        chartsInstance.current?.setOption(option);
    }, [option]);

    return <div className={`w-full h-full ${customClassName}`} ref={chartsNode} />;
}

export default CustomCharts;