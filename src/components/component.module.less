/* 添加以下过渡效果 */
.dynamic_underline {
  transition-property: left, width;
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #6253e1, #098fc3);
}

/* 添加以下全局样式 */
.hover_underline {

  background: linear-gradient(135deg, #6253e1, #098fc3);
}

.theme_switch {
  /* The switch - the box around the slider */
  .switch {
    font-size: 16px;
    position: relative;
    display: inline-block;
    width: 3.3em;
    height: 1.9em;
    cursor: pointer;
    transform-style: preserve-3d;
    perspective: 500px;
  }

  /* Hide default HTML checkbox */
  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  /* The slider */
  .slider {
    --background: #20262c;

    position: absolute;
    cursor: pointer;
    inset: 0;
    background-color: var(--background);
    transition: 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 25px;
    overflow: hidden;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 70% 50%, rgba(255, 255, 255, 0.15), transparent 60%);
      opacity: 0;
      transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
    }
  }

  .slider::before {
    position: absolute;
    content: "";
    height: 1.3em;
    width: 1.3em;
    border-radius: 50%;
    left: 10%;
    bottom: 15%;
    box-shadow:
      inset 8px -4px 0 0 #ececd9,
      -4px 1px 4px 0 #dadada;
    background: var(--background);
    transition: 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-origin: center center;
  }

  .decoration {
    position: absolute;
    content: "";
    height: 2px;
    width: 2px;
    border-radius: 50%;
    right: 20%;
    top: 15%;
    background: #e5f041e6;
    backdrop-filter: blur(10px);
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow:
      -7px 10px 0 #e5f041e6,
      8px 15px 0 #e5f041e6,
      -17px 1px 0 #e5f041e6,
      -20px 10px 0 #e5f041e6,
      -7px 23px 0 #e5f041e6,
      -15px 25px 0 #e5f041e6;
    opacity: 0.9;
    filter: blur(0.3px);
  }

  input:checked ~ .decoration {
    transform: translateX(-20px) rotate(15deg);
    width: 6px;
    height: 6px;
    background: white;
    box-shadow:
      -12px 0 0 white,
      -6px 0 0 1.6px white,
      5px 15px 0 1px white,
      1px 17px 0 white,
      10px 17px 0 white;
    opacity: 1;
    filter: blur(0);
  }

  input:checked + .slider {
    background-color: #5494de;
    
    &::after {
      opacity: 1;
    }
  }

  input:checked + .slider::before {
    transform: translateX(100%) rotate(25deg);
    box-shadow:
      inset 15px -4px 0 15px #efdf2b,
      0 0 10px 0 #efdf2b,
      0 0 20px 5px rgba(239, 223, 43, 0.3);
  }
  
  /* 添加悬停效果 */
  .switch:hover .slider::before {
    transform: scale(1.05) translateY(-2px);
  }
  
  .switch:hover input:checked + .slider::before {
    transform: translateX(100%) rotate(25deg) scale(1.05) translateY(-2px);
  }
}
