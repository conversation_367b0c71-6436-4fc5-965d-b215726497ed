import {Pagination, Table} from "antd";
import type { TableProps } from 'antd';
import { useRef, useEffect, useState } from 'react';
import { createStyles } from 'antd-style';
import { useTranslation } from 'react-i18next';
import type { Key } from 'react';
import type { SelectableTablePropsType } from './type';

const useStyle = createStyles(({ css }) => {
  return {
    customTable: css`
      .ant-table {
        .ant-table-container {
          .ant-table-body {
            scrollbar-width: thin;
            scrollbar-gutter: stable;
            transition: none !important;
          }
        }
      }
    `,
  };
});

export default function TableSelected<T extends { key: string }>({
    columns,
    dataSource,
    className,
    total,
    callbackPages,
    callbackOnRows,
    tableProps,
}: Readonly<SelectableTablePropsType<T>>) {
    const { styles } = useStyle();
    const { t } = useTranslation();
    const containerRef = useRef<HTMLDivElement>(null);
    const [scrollY, setScrollY] = useState<number>(0);
    const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

    const finalTableProps: TableProps<T> = {
        ...tableProps,
        rowSelection: {
            ...tableProps?.rowSelection,
            selectedRowKeys,
            onChange: (newSelectedRowKeys: Key[], selectedRows: T[]) => {
                setSelectedRowKeys(newSelectedRowKeys);
                callbackOnRows(selectedRows);
            },
        },
    };

    // 添加行点击处理函数
    const onRow = (record: T) => ({
        onClick: (event: React.MouseEvent) => {
            return;
            // 展示屏蔽这个功能
            // 如果点击的是按钮，不触发选择
            // if ((event.target as HTMLElement).tagName === 'BUTTON') {
            //     return;
            // }
            // if ((event.target as HTMLElement).tagName === 'svg') {
            //   return;
            // }
            // const key = record.key;
            // const newSelectedRowKeys = selectedRowKeys.includes(key)
            //     ? selectedRowKeys.filter((k) => k !== key)
            //     : [...selectedRowKeys, key];
            // setSelectedRowKeys(newSelectedRowKeys);
            // const selectedRows = dataSource.filter(item => 
            //     newSelectedRowKeys.includes(item.key)
            // );
            // callbackOnRows(selectedRows);
        },
    });

    useEffect(() => {
        if (containerRef.current) {
            const updateScrollY = () => {
                const height = containerRef.current?.clientHeight || 0;
                // 减去分页器和其他边距的高度
                setScrollY(height - 90);
            };

            updateScrollY();
            
        }
    }, []);

    return (
      <div
        ref={containerRef}
        className={`w-full h-full border border-gray-200 dark:border-gray-500 rounded overflow-hidden ${className || ''}`}
      >
        <Table<T>
          {...finalTableProps}
          className={styles.customTable}
          pagination={false}
          columns={columns}
          dataSource={dataSource}
          scroll={{
            x: 'max-content',
            y: scrollY,
            scrollToFirstRowOnChange: true,
          }}
          onRow={onRow}
        />
        <div className="flex justify-end my-1 px-3">
          <Pagination
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => t('antd.pagination.total', { total })}
            onChange={(page, pageSize) => callbackPages(page, pageSize)}
          />
        </div>
      </div>
    );
}