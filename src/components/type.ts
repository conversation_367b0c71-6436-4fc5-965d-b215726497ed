import type React from "react";
import type { EChartsOption } from 'echarts/types/dist/echarts';
import type {TableProps} from "antd";
import type { ColumnType, ColumnGroupType } from 'antd/es/table';

export interface TreeProps {
  isGroup?: boolean;
  children: React.ReactNode;
}

export interface AuthData {
  token: string;
  permissions: string[]; // 权限标识数组 ['user:read', 'user:delete']
  userInfo: {
    id: number;
    name: string;
    avatar?: string;
    role: string;
  };
  expiresAt: number;
}

export type PageType = "system" | "equipment" | "map" | "report" | "user" | "fault";
export type LanguageType = "en" | "zh-CN" | "es" | "ro";

export interface ChartsPropsType {
  customClassName?: string;
  option: EChartsOption;
}

export interface TablePropsType<T> {
  columns: TableProps<T>['columns'];
  dataSource: T[];
  className?: string;

  total: number;
  callbackPages: (page: number, pageSize: number) => void;
  tableProps: TableProps<T>;
}

// 可选择行的表格属性接口，继承自TablePropsType，但使callbackOnRows成为必传
export interface SelectableTablePropsType<T> extends Omit<TablePropsType<T>, 'callbackOnRows'> {
  callbackOnRows: (data: T[]) => void; // 选中行回调必传
}