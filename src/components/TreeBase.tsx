import type { TreeProps } from "@/components/type";
import type React from "react";
import { useEffect, useMemo, useState } from "react";
import { Tree, Input, type TreeDataNode } from "antd";
import {LeftOutlined, RightOutlined, SearchOutlined} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useAtom } from "jotai";
import { ProjectAtom, SelectedProjectAtom } from "@/jotai/atoms";
import { createStyles } from 'antd-style';

const useStyle = createStyles(({ css }) => {
  return {
    hideScrollbar: css`
      /* 隐藏滚动条但保持可滚动 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none;  /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
    `
  };
});

export default function TreeCustom({ isGroup = true, children}: Readonly<TreeProps>) {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState("");
  const { t } = useTranslation();

  const [, setSelectedProject] = useAtom(SelectedProjectAtom)

  const [treeData, setTreeData] = useAtom(ProjectAtom)
  const { styles } = useStyle();

  useEffect(() => {
    const data:TreeDataNode[] = [
      {
        title: "parent 1",
        key: "00",
        children: [
          {
            title: "parent 1-0",
            key: "01",
            children: [
              { title: "leaf", key: "010" },
              {
                title: "Test",
                key: "011",
              },
              { title: "leaf", key: "012" },
            ],
          },
          {
            title: "parent 1-1",
            key: "0-0-1",
            children: [{ title: "leaf", key: "0-0-1-0" }],
          },
          {
            title: "parent 1-2",
            key: "0-0-2",
            children: [
              { title: "leaf", key: "0-0-2-0" },
              {
                title: "leaf",
                key: "0-0-2-1",
              },
            ],
          },
        ],
      },
      {
        title: "parent 2",
        key: "0-1",
        children: [
          {
            title: "parent 2-0",
            key: "0-1-0",
            children: [
              { title: "leaf", key: "0-1-0-0" },
              { title: "leaf", key: "0-1-0-1" },
            ],
          },
        ],
      },
    ]
    setTreeData(data)
  }, []);


  // 这里获取TreeData的key和title组成数组
  const dataList: { key: React.Key; title: string }[] = [];
  const generateList = (data: TreeDataNode[]) => {
    for (const element of data) {
      const node = element;
      const { key, title } = node;
      dataList.push({ key, title: title as string });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(treeData);
  const getParentKey = (key: React.Key, tree: TreeDataNode[]): React.Key => {
    let parentKey: React.Key;
    for (const element of tree) {
      const node = element;
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    return parentKey!;
  };
  // 搜索功能
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const newExpandedKeys = dataList
      .map((item) => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, treeData);
        }
        return null;
      })
      .filter((item, i, self): item is React.Key => !!(item && self.indexOf(item) === i));
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
  };

  const onSelectHandel = (keys: React.Key[], info: { node: { key: React.Key } }) => {
    const clickedKey = info.node.key;
    if (selectedKeys.includes(clickedKey)) {
      return;
    }
    setSelectedKeys([clickedKey]);
    if (keys.length > 0) {
      setExpandedKeys(keys);
    }
    // 这里 后续需要根据数据进行调整，是否需要解析器来解析数据，获取pid，road—id，gid，zid
    setSelectedProject({ keys: keys, info: info})
  };

  const treeDataHandle = useMemo(() => {
    if (searchValue === "") {
      setExpandedKeys([]);
    }
    const loop = (data: TreeDataNode[]): TreeDataNode[] =>
      data.map((item) => {
        const strTitle = item.title as string;
        const index = strTitle.indexOf(searchValue);
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
          index > -1 ? (
            <span key={item.key}>
              {beforeStr}
              <span style={{ color: "red" }}>{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span key={item.key}>{strTitle}</span>
          );
        if (item.children) {
          return { title, key: item.key , children: loop(item.children) };
        }

        return {
          title,
          key: item.key,
          disabled: !isGroup
        };
      });

    return loop(treeData);
  }, [searchValue, treeData]);

  const onExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  const [isShowTree, setIsShowTree] = useState<boolean>(true)

  return (
      <div className={'flex w-full h-full'}>
        <div className={`h-full flex-none transition-all duration-300 ${
            isShowTree ? "w-[246px]" : "w-0"
        }`}>
          <div className={`w-[246px] flex h-full shadow-lg box-border p-2 rounded-lg bg-white dark:bg-gray-800 flex-col relative transition-transform duration-300 ${isShowTree ? "translate-x-0" : "-translate-x-[95%]"}`}>
            {/* 按钮样式调整 */}
            <button type="button" onClick={() => setIsShowTree(!isShowTree)}
                className={`flex items-center justify-center absolute ${isShowTree ? "right-[-6px]" : "right-[3px]"} w-[12px] h-[48px] bottom-[50%] bg-white dark:bg-gray-800 border-gray-800 dark:border-gray-500 border-2 rounded-full z-10 cursor-pointer !transition-all !duration-300`}>
                          {isShowTree ? <LeftOutlined style={{ fontSize: '8px'}} className={'!text-gray-900 dark:!text-gray-300'}/> : <RightOutlined style={{ fontSize: '8px' }}  className={'!text-gray-900 dark:!text-gray-100'}/>}
            </button>

            {/* 添加动画容器 */}
            <div
                className={`h-full ${isShowTree ? "opacity-100" : "opacity-0"} transition-opacity duration-300 overflow-hidden`}>
              {/* 原有内容容器 */}
              <div style={{ backgroundColor: "transparent" }} className={"mb-2 h-fit w-full box-border"}>
                <Input placeholder={t("search_project")} style={{ backgroundColor: "transparent",width: '100%' }} allowClear onChange={onChange} suffix={<SearchOutlined />} />
              </div>
              <div className={"w-full flex-1 overflow-auto"}>
                <Tree
                    rootStyle={{ backgroundColor: "transparent" }}
                    expandedKeys={expandedKeys}
                    selectedKeys={selectedKeys}
                    onExpand={onExpand}
                    autoExpandParent={true}
                    showLine={true}
                    blockNode={true}
                    onSelect={onSelectHandel}
                    treeData={treeDataHandle}
                />
              </div>
            </div>
          </div>
        </div>
        <div className={`flex-1 h-full overflow-auto bg-white dark:bg-gray-800 transition-all duration-300 ${isShowTree? "ml-2": "ml-4"} shadow p-2 rounded-lg ${styles.hideScrollbar}`}>
          {children}
        </div>
      </div>
  );
}
