import { Segmented } from "antd";
import TreeBase from "@/components/TreeBase";
import {AppstoreOutlined, BarsOutlined} from "@ant-design/icons"; // 新增：导入 Segmented 组件
// import {useTranslation} from "react-i18next";
import { Outlet, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";

export default function EquipmentManagement() {
      // 使用从useNavigate获取的navigate函数进行路由跳转
    const navigate = useNavigate();
    // const {t} = useTranslation();
    const [actSegmented, setActSegmented] = useState<string>("/equipment/overview/iot");
    const handelToUrl = (url:string) =>{
        navigate(url);
        setActSegmented(url)
    }
    useEffect(() => {
        setActSegmented(window.location.pathname);
    }, [])
    return (
      <TreeBase>
        <div className="h-full w-full flex flex-col">
          <div className="w-full p-2 bg-whit  border border-gray-300/50 dark:border-white/20 dark:bg-gray-700 shadow backdrop-blur-lg rounded-lg mb-2">
            <Segmented
              size="large"
              onChange={(value) => {
                handelToUrl(value);
              }}
              value={actSegmented}
              options={[
                {
                  label: 'IOT设备',
                  value: '/equipment/overview/iot',
                  icon: <BarsOutlined />,
                },
                {
                  label: '网关',
                  value: '/equipment/overview/gateway',
                  icon: <AppstoreOutlined />,
                },
                {
                  label: '智能座椅',
                  value: 'Kanban1',
                  icon: <AppstoreOutlined />,
                },
                {
                  label: '智慧灯杆',
                  value: 'Kanban2',
                  icon: <AppstoreOutlined />,
                },
              ]}
            />
          </div>
          <div className="w-full" style={{height:'calc(100% - 66px)'}}>
            <Outlet />
          </div>
        </div>
      </TreeBase>
    );
}