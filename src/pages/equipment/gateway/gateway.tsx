import { useTranslation } from "react-i18next";
import { Tag, <PERSON>, But<PERSON>, Divider, Popconfirm, type PopconfirmProps, message } from "antd";
import { useState } from "react";
import TableSelected from "@/components/TableSelected";

interface GatewayData {
    key: string;
    name: string;
    ip: string;
    mac: string;
    status: 'online' | 'offline' | 'fault';
    lastOnlineTime: string;
    location: string;
}

export default function Gateway() {
    const { t } = useTranslation();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    // 模拟网关数据
    const mockData: GatewayData[] = Array.from({ length: 50 }).map((_, index) => ({
        key: index.toString(),
        name: `Gateway-${index + 1}`,
        ip: `192.168.1.${index + 1}`,
        mac: `00:1B:44:11:3A:${index.toString().padStart(2, '0')}`,
        status: ['online', 'offline', 'fault'][Math.floor(Math.random() * 3)] as 'online' | 'offline' | 'fault',
        lastOnlineTime: new Date().toISOString(),
        location: `Location ${index + 1}`
    }));

    const getStatusTag = (status: string) => {
        const statusConfig = {
            online: { color: 'success', text: t('status.online') },
            offline: { color: 'default', text: t('status.offline') },
            fault: { color: 'error', text: t('status.fault') }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
    };

    const columns = [
        {
            title: t('gateway.name'),
            dataIndex: 'name',
            key: 'name',
            width: '15%',
            align: 'center' as const
        },
        {
            title: t('gateway.ip'),
            dataIndex: 'ip',
            key: 'ip',
            width: '15%',
            align: 'center' as const
        },
        {
            title: t('gateway.mac'),
            dataIndex: 'mac',
            key: 'mac',
            width: '20%',
            align: 'center' as const
        },
        {
            title: t('gateway.status'),
            dataIndex: 'status',
            key: 'status',
            width: '10%',
            align: 'center' as const,
            render: (status: string) => getStatusTag(status)
        },
        {
            title: t('gateway.lastOnlineTime'),
            dataIndex: 'lastOnlineTime',
            key: 'lastOnlineTime',
            width: '20%',
            align: 'center' as const
        },
        {
            title: t('gateway.location'),
            dataIndex: 'location',
            key: 'location',
            width: '10%',
            align: 'center' as const
        },
        {
            title: t('common.operation'),
            key: 'operation',
            width: '10%',
            align: 'center' as const,
            render: (_: any, record: GatewayData) => (
                <Space size="middle">
                    <Button type="link" onClick={() => console.log('Edit:', record)}>
                        {t('common.edit')}
                    </Button>
                    <Button type="link" danger onClick={() => console.log('Delete:', record)}>
                        {t('common.delete')}
                    </Button>
                </Space>
            )
        }
    ];

    const handlePageChange = (page: number, size: number) => {
        setCurrentPage(page);
        setPageSize(size);
        // 这里可以添加实际的数据获取逻辑
    };

    const startIndex = (currentPage - 1) * pageSize;
    const currentData = mockData.slice(startIndex, startIndex + pageSize);


    const confirm: PopconfirmProps['onConfirm'] = (e) => {
        console.log(e);
        message.success('Click on Yes');
      };
      
      const cancel: PopconfirmProps['onCancel'] = (e) => {
        console.log(e);
        message.error('Click on No');
      };
    return (
            <div className="card-base p-2 rounded-lg w-full h-full flex flex-col">
                <div className="flex gap-2 justify-start items-center">
                    <Button type="primary">{t('gateway.add')}</Button>
                    <Popconfirm
                        placement="bottom"
                        title="Delete the task"
                        description="Are you sure to delete this task?"
                        onConfirm={confirm}
                        onCancel={cancel}
                        okText="Yes"
                        cancelText="No"
                    >
                        <Button danger>Delete</Button>
                    </Popconfirm>
                </div>
                <Divider className="!my-[8px]"/>
                <div className="flex-1">
                    <TableSelected<GatewayData>
                        callbackOnRows={(rows) => {
                            console.log(rows);
                        }}
                        columns={columns}
                        dataSource={currentData}
                        total={mockData.length}
                        callbackPages={handlePageChange} tableProps={{}} />
                </div>
            </div>
    );
}