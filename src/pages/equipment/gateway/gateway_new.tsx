import { useTranslation } from "react-i18next";
import { Tag, Space, But<PERSON>, Divider, Popconfirm, type PopconfirmProps, message, Tooltip, Input } from "antd";
import { useState } from "react";
import TableSelected from "@/components/TableSelected";
import { MoreOutlined } from "@ant-design/icons";
import StatusIcons from "@/assets/svgIcons/StatusIcons";
import NodeIcons from "@/assets/svgIcons/NodeIcons";
import MapIcons from "@/assets/svgIcons/MapIcons";

interface GatewayData {
    key: string;
    name: string;
    ip: string;
    mac: string;
    status: 'online' | 'offline' | 'fault';
    lastOnlineTime: string;
    location: string;
}

export default function GatewayPage() {
    const { t } = useTranslation();
    const [showTopMask, setShowTopMask] = useState(false);
    const [showBottomMask, setShowBottomMask] = useState(false);

    // 模拟网关数据
    const mockData: GatewayData[] = Array.from({ length: 10 }).map((_, index) => ({
        key: index.toString(),
        name: `Gateway-${index + 1}`,
        ip: `192.168.1.${index + 1}`,
        mac: `00:1B:44:11:3A:${index.toString().padStart(2, '0')}`,
        status: ['online', 'offline', 'fault'][Math.floor(Math.random() * 3)] as 'online' | 'offline' | 'fault',
        lastOnlineTime: new Date().toISOString(),
        location: `Location ${index + 1}`
    }));



    const confirm: PopconfirmProps['onConfirm'] = (e) => {
        console.log(e);
        message.success('Click on Yes');
      };
      
      const cancel: PopconfirmProps['onCancel'] = (e) => {
        console.log(e);
        message.error('Click on No');
      };
    return (
            <div className="card-base  rounded-lg w-full h-full flex flex-col">
                <div className="flex m-2 mb-0 justify-between items-center">
                    <div className="flex justify-start gap-2">
                        <Button type="primary">{t('gateway.add')}</Button>
                        <Popconfirm
                            placement="bottom"
                            title="Delete the task"
                            description="Are you sure to delete this task?"
                            onConfirm={confirm}
                            onCancel={cancel}
                            okText="Yes"
                            cancelText="No"
                        >
                            <Button danger>Delete</Button>
                        </Popconfirm>
                    </div>
                    <div>
                        <Input placeholder="Ctrl+K  Search Gateway">
                        </Input>
                    </div>
                </div>
                <Divider className="!mt-[8px] !mb-0"/>
                <div 
                    className="p-1 pt-[8px] overflow-y-auto h-[calc(100vh-220px)] relative"
                    onScroll={(e) => {
                        const target = e.target as HTMLDivElement;
                        const isScrollable = target.scrollHeight > target.clientHeight;
                        const isAtTop = target.scrollTop === 0;
                        const isAtBottom = Math.abs(target.scrollHeight - target.clientHeight - target.scrollTop) < 1;
                        
                        setShowTopMask(isScrollable && !isAtTop);
                        setShowBottomMask(isScrollable && !isAtBottom);
                    }}
                >
                    {showTopMask && (
                        <div className="fixed top-12 left-0 right-0 h-12 bg-gradient-to-b from-white/90 via-white/50 to-transparent z-50 dark:from-gray-700/95 dark:via-gray-700/60 dark:to-transparent pointer-events-none">
                        </div>
                    )}
                    {showBottomMask && (
                        <div className="fixed bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-white/90 via-white/50 to-transparent z-50 dark:from-gray-700/95 dark:via-gray-700/60 dark:to-transparent pointer-events-none">
                        </div>
                    )}
                    <div className="px-1 w-full h-fit flex flex-wrap gap-4">
                    {
                        mockData.map((item) => (
                            <div key={item.key} className="overflow-hidden w-[calc(25%-12px)] min-w-[320px] card-base dark:bg-gray-800 rounded-lg p-4 h-fit flex flex-col transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-lg hover:shadow-gray-400/20 dark:hover:shadow-gray-900/30">
                                <div className="flex justify-between items-center">
                                    <span className="font-medium text-xl text-gray-950 dark:text-gray-200">{item.name}</span>
                                    <MoreOutlined className="!text-xl font-black dark:!text-gray-400"/>
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                    <div>
                                        <div>MAC: {item.mac}</div>
                                    </div>
                                    <div className="flex justify-between mt-3 items-center">
                                        <div className="flex">
                                            <StatusIcons color="#1afa29"/>
                                            <span className="ml-1">{item.status}</span>
                                        </div>
                                        <div className="flex items-center">
                                            <Tooltip title="Nodes" color={"#2db7f5"} className="mr-3">
                                                <div className="flex items-center">
                                                    <NodeIcons color="#1296db" width={15} height={15}/>
                                                    <span className="ml-1">32</span>
                                                </div>
                                            </Tooltip>
                                            <MapIcons color="#707070"/>
                                        </div>
                                    </div>
                                </div>
                                <Button className="mt-2 !bg-gray-100 hover:!bg-gray-200 dark:!bg-gray-600 !border-0">View Details</Button>
                            </div>
                        ))
                    }
                    </div>
                </div>
            </div>
    );
}