import { SearchOutlined } from "@ant-design/icons";
import { Input, Pagination, Radio, Segmented, Select } from "antd";
import type { CheckboxGroupProps } from "antd/es/checkbox";
import { useEffect, useState, useRef } from "react";
import SimpleIotPage from "./components/SimpleViews";
import DetailsIotPage from "./components/DetailsViews";

export default function DevicePage() {

    const handleChange = (value: string) => {
        console.log(`selected ${value}`);
    };
    const options: CheckboxGroupProps<string>['options'] = [
        { label: '简约视图', value: 'simple' },
        { label: '列表视图', value: 'details' },
    ];

    const [viewMode, setViewMode] = useState<string>('simple');
    useEffect(() => {
        setViewMode(localStorage.getItem('viewMode') || 'simple');
    })
    const handleViewMode = (e: any) => {

        if (e === 'simple') {
            setViewMode('simple');
        } else {
            setViewMode('details');
        }
        localStorage.setItem('viewMode', e);
    };


    
    const containerRef = useRef<HTMLDivElement>(null);
    const [columns, setColumns] = useState(1);

    useEffect(() => {
        const handleResize = () => {
            if (containerRef.current) {
              
                const containerWidth = containerRef.current.offsetWidth;
                setColumns(Math.min(Math.floor(containerWidth / 405), 4));
            }
        };

        // 初始化时计算一次
        handleResize();
        
        // 创建 ResizeObserver 监听容器尺寸变化
        const resizeObserver = new ResizeObserver(handleResize);
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }

        return () => {
            resizeObserver.disconnect();
        };
    }, []);
    return (
      <div className="flex flex-col pb-0 w-full h-full card-base rounded-lg p-1">
        <div className="flex justify-between p-1">
          <Segmented<string>
            options={['全部设备', '在线', '离线', '故障', '开灯', '关灯']}
            onChange={(value) => {
              console.log(value); // string
            }}
          />
          <div className="flex items-center">
            <Input
              placeholder="搜索设备名称/SN"
              className="!w-[220px]"
              prefix={<SearchOutlined />}
            />
            <Select
              className="!ml-2"
              defaultValue="default"
              style={{ width: 160 }}
              onChange={handleChange}
              options={[
                { value: 'default', label: '默认排序' },
                { value: 'az_name', label: 'A-Z按名称' },
                { value: 'date_up', label: '按最后上传时间升序' },
                { value: 'date_down', label: '按最后上传时间降序' },
                { value: 'electricity', label: '按电量' },
              ]}
            />
          </div>
        </div>
        <div className="flex-1 pt-1 mb-1 relative overflow-hidden">
          <div className="z-10 flex w-[calc(100%-4px)] absolute top-0 left-0 items-center justify-between p-1 bg-gradient-to-b from-white via-white/70 to-transparent dark:from-gray-700 dark:via-gray-700/70 dark:to-transparent backdrop-blur-[2px] transition-all duration-300">
            <Radio.Group
              block
              options={options}
              value={viewMode}
              optionType="button"
              buttonStyle="solid"
              onChange={(e) => handleViewMode(e.target.value)}
            />
            <div>
              <span className="text-gray-500 text-sm mr-2">共85条数据</span>
            </div>
          </div>
          <div
            ref={containerRef}
            className={`h-full pt-[44px] pb-[50px] grid gap-5 ${viewMode==='simple'?'auto-rows-max':'grid-cols-1'} p-1 overflow-auto`}
            style={{
              gridTemplateColumns: viewMode === 'simple'
                ? `repeat(${columns}, minmax(400px, 610px))`
                : '1fr',
              justifyContent: 'center',
            }}
          >
            {[...new Array(20)].map((_,index) => (
              <div
                key={`device-${// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
index}`}
              >
                {viewMode === 'simple' ? (
                    <SimpleIotPage />
                ): (
                    <DetailsIotPage />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center items-center h-[40px] w-[calc(100%-4px)] absolute bottom-0 left-0 bg-gradient-to-t from-white via-white/70 to-transparent dark:from-gray-700 dark:via-gray-700/70 dark:to-transparent backdrop-blur-[2px] transition-all duration-300">
            <Pagination
              total={85}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `Total ${total} items`}
            />


          </div>
        </div>
      </div>
    );
}