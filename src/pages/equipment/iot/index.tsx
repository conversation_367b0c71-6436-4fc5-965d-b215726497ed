import AuthCheck from "@/auth/AuthCheck";
import TableSelected from "@/components/TableSelected";
import { AppstoreAddOutlined, DownloadOutlined, LayoutOutlined, RedoOutlined } from "@ant-design/icons";
import {Button, Checkbox, type CheckboxOptionType, Divider, Drawer, Input, type InputNumberProps, Popover, Slider, type SliderSingleProps, type TableColumnsType, Tooltip} from "antd";
import type { SearchProps } from "antd/lib/input";
import type { ColumnType } from "antd/lib/table";
import classNames from "classnames";
import { useState, useMemo } from "react";
import {useTranslation} from "react-i18next";

export default function IotPage() {
    const {t} = useTranslation();
    const [openBrightness, setOpenBrightness] = useState(false);
    const [brightnessValue, setBrightnessValue] = useState<number>(0);
    const [isRotating, setIsRotating] = useState(false);
    
    const hideBrightness = () => {
        setOpenBrightness(false);
    };

    const handleOpenBrightnessChange = (newOpen: boolean) => {
        setOpenBrightness(newOpen);
    };
    
    const onBrightnessChange: InputNumberProps['onChange'] = (newValue) => {
        setBrightnessValue(newValue as number);
    };
    
    const formatter: NonNullable<SliderSingleProps['tooltip']>['formatter'] = (value) => `${value}%`;
    const marks: SliderSingleProps['marks'] = {
        0: '0%',
        30: '30%',
        50: '50%',
        80: '80%',
        100: '100%',
    };
    
    const handelSureClick = () => {
        hideBrightness();
    };
    const handelQuickClick = (brightness: number) => {
        setBrightnessValue(brightness);
        hideBrightness();
    };

    // 使用 useMemo 记忆化 SliderModel 组件
    const SliderModel = useMemo(() => {
        return (
            <Popover
                content={
                    <div className="w-[300px] p-2">
                        <div className={'flex justify-between'}>
                            <Button danger size={'small'} onClick={()=>handelQuickClick(0)}>{t("control.off")}</Button>
                            <Button size={'small'} type="default" onClick={()=>handelQuickClick(30)}>30%</Button>
                            <Button size={'small'} type="default" onClick={()=>handelQuickClick(50)}>50%</Button>
                            <Button size={'small'} type="default" onClick={()=>handelQuickClick(80)}>80%</Button>
                            <Button size={'small'} type="primary" onClick={handelSureClick}>{t("full.sure")}</Button>
                        </div>
                        <div className={"flex items-center"}>
                            <Slider
                                min={1}
                                max={100}
                                tooltip={{ formatter }}
                                className="w-[300px]"
                                marks={marks}
                                onChange={onBrightnessChange}
                                value={brightnessValue}
                            />
                        </div>
                    </div>
                }
                placement="bottom"
                trigger="hover"
                open={openBrightness}
                onOpenChange={handleOpenBrightnessChange}
                forceRender={true}
            >
                <Button type="primary">{t("control.dimming")}</Button>
            </Popover>
        );
    }, [openBrightness, brightnessValue, t]);
    
    const onSearch: SearchProps['onSearch'] = (value, _e, info) => console.log(info?.source, value);
    

    // Table
    interface DataType {
        key: string;
        name: string;
        age: number;
        address: string;
      }
      
    const columns: TableColumnsType<DataType> = [
        { title: 'Column 1', dataIndex: 'name', key: '1' },
        { title: 'Column 2', dataIndex: 'age', key: '2' },
        { title: 'Column 3', dataIndex: 'address', key: '3' },
    ];
    const originData = Array.from({ length: 10 }).map<DataType>((_, i) => ({
        key: i.toString(),
        name: `Edward ${i}`,
        age: 32,
        address: `London Park no. ${i}`,
      }));
    const defaultCheckedList = columns.map((item) => item.key);
    const [checkedList, setCheckedList] = useState(defaultCheckedList);
    const CheckboxOptions = columns.map(({ key, title }) => ({
        label: title,
        value: key,
      }));
    const newColumns = columns.map((item) => ({
        ...item,
        hidden: !checkedList.includes(item.key as string),
      }));
    const TableListShow = (
        <div className="flex flex-col justify-center items-center  cursor-default">
            <Checkbox.Group
                className="flex flex-col gap-1 "
                value={checkedList}
                options={CheckboxOptions as CheckboxOptionType[]}
                onChange={(value) => {
                    setCheckedList(value as string[]);
                }}
            />
        </div>
    )
    const FormatList = ['JSON', 'CSV', 'XML', 'YAML', 'TXT','PNG',"PDF","DOCX","XLSX","XLS"]
    const handelDownloadFile = (format: string) => {
        console.log(format)
    }
    const TableDownloadFormat = (
        <div className={"flex flex-col justify-center items-center  cursor-default"}>
            {FormatList.map((item) => (
                <button
                key={item}
                type="button"
                onClick={() => {
                    handelDownloadFile(item);
                }}
                className={classNames(
                    "pl-2 w-[100px] h-[30px] text-md leading-[30px] text-center hover:bg-gray-200 hover:dark:bg-gray-600 rounded dark:text-gray-400",
                )}
                >
                {item}
                </button>
            ))}
        </div>
    )
    // Drawer Gateway
    const [openDrawerGateway, setOpenDrawerGateway] = useState(false);
    const showDrawer = () => {
        setOpenDrawerGateway(true);
      };
    
      const onCloseGateway = () => {
        setOpenDrawerGateway(false);
      };
    return (
        <div className="w-full h-full flex flex-col">
            {/* IOT设备表格 */}
            <div className="w-full flex-1 p-2 bg-white border border-gray-300/50 dark:border-white/20 dark:bg-gray-700 shadow backdrop-blur-lg rounded-lg">
                <div className=" flex justify-between items-center">
                    <div className="flex gap-1 flex-1 overflow-auto items-center justify-start">
                        <AuthCheck requiredPerm="iot:control">
                            {SliderModel}
                        </AuthCheck>
                        <AuthCheck requiredPerm="iot:view">
                            <Button type="primary">{t("control.read_data")}</Button>
                        </AuthCheck>
                        <AuthCheck requiredPerm="iot:modify">
                            <Button type="primary">{t("control.add")}</Button>
                        </AuthCheck>
                    </div>
                    <div className="flex h-[32px] items-center gap-1 justify-end">
                        <Input.Search placeholder={t("control.search")} onSearch={onSearch} style={{ width: 180 }} />
                        <div className="card-base  rounded-md bg-gray-200 dark:bg-gray-800 flex items-center gap-1">
                            <button type="button" className={classNames("w-8 h-[30px] flex items-center justify-center", {"animate-spin": isRotating})} onClick={() => {
                                setIsRotating(true);
                                setTimeout(() => setIsRotating(false), 1000);
                            }}>
                                <RedoOutlined className="!text-gray-900 dark:!text-gray-300" />
                            </button>
                            <Divider type="vertical"  className="!mx-[0]"/>
                            <Popover placement="bottomRight" content={TableListShow} arrow={false} forceRender={true}>
                                <div className="w-8 h-[30px] flex items-center justify-center">
                                    <AppstoreAddOutlined className="!text-gray-800 dark:!text-gray-300" />
                                </div>
                            </Popover>
                            <Divider type="vertical"  className="!mx-[0]"/>
                            <Popover placement="bottomRight" content={TableDownloadFormat} arrow={false} forceRender={true}>
                                <div className="w-8 h-[30px] flex items-center justify-center">
                                    <DownloadOutlined className="!text-gray-800 dark:!text-gray-300" />
                                </div>
                            </Popover>
                            <Divider type="vertical"  className="!mx-[0]"/>
                            <Tooltip placement="topLeft" title={'Gateway Control'} arrow={false}>
                                <button type="button" onClick={showDrawer} className="w-8 h-[30px] flex items-center justify-center">
                                    <LayoutOutlined className="!text-gray-800 dark:!text-gray-300" />
                                </button>
                            </Tooltip>
                            <Drawer title="Gateway Control" size="large" onClose={onCloseGateway} open={openDrawerGateway}>
                                <p>Some contents...</p>
                                <p>Some contents...</p>
                                <p>Some contents...</p>
                            </Drawer>
                        </div>
                    </div>
                </div>
                <Divider className="!my-[8px]"/>
                <div className="h-[calc(100%-50px)]">
                    <TableSelected<DataType>
                        callbackOnRows={(rows) => {
                            console.log(rows);
                        }}
                        columns={columns as ColumnType<DataType>[]}
                        dataSource={originData}
                        total={100}
                        callbackPages={() => {
                            // 处理分页变化
                          }}
                        tableProps={{
                            
                        }}
                    />
                </div>
            </div>
        </div>
    )
}
