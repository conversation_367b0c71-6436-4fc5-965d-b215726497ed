import {<PERSON><PERSON>, Input, <PERSON>lide<PERSON>} from "antd";
import {CheckOutlined, CloseOutlined, EditOutlined} from "@ant-design/icons";
import {useState} from "react";
import LampIcon from "@/assets/svgIcons/LampIcons.tsx";
import BatteryIcon from "@/assets/svgIcons/BatteryIcon";
import { BatteryCharging, RotateCw, Zap } from 'lucide-react';
import PowerIcon from "@/assets/svgIcons/PowerIcon";

export default function DetailsIotPage(props: Readonly<Record<string, any>>) {
    const data = props;
    // console.log(data);
    const [isDeviceInput, setIsDeviceInput] = useState<boolean>(false);
    const [deviceName, setDeviceName] = useState<string>('');
    const SaveIotDeviceName = () => {
        setIsDeviceInput(false);
        console.log(deviceName);
    }
    return (
      <div className="w-full h-fit card-base grid grid-cols-14 gap-8 rounded-lg p-4 text-gray-800 dark:text-white dark:bg-gray-800">
        <div className={'flex flex-col justify-between col-span-3'}>
          <div className="">
            <div className={'flex justify-start items-center gap-2'}>
              {isDeviceInput ? (
                <Input
                  value={deviceName}
                  onChange={(e) => setDeviceName(e.target.value)}
                  placeholder=""
                  variant="underlined"
                  className={'!font-bold !h-[24px]'}
                  autoFocus
                  onPressEnter={SaveIotDeviceName}
                />
              ) : (
                <span className={'font-bold'}>设备详情</span>
              )}
              {isDeviceInput ? (
                <div className={'flex gap-2'}>
                  <CheckOutlined
                    className={'!text-gray-400 !text-sm'}
                    onClick={SaveIotDeviceName}
                  />
                  <CloseOutlined
                    className={'!text-gray-400 !text-sm'}
                    onClick={() => setIsDeviceInput(false)}
                  />
                </div>
              ) : (
                <EditOutlined
                  className={'!text-gray-400 !text-sm'}
                  onClick={() => setIsDeviceInput(true)}
                />
              )}
            </div>
            <div className="text-gray-500 text-sm my-1">
              <span>SN: BEE0000000</span>
            </div>
          </div>
          <div
            className={
              'text-gray-500 text-sm flex items-center gap-2 whitespace-nowrap'
            }
          >
            <span>最近上传时间: 2023-07-01 12:00:00</span>
            <RotateCw className="h-4 w-4" />
          </div>
        </div>
        <div className="col-span-3">
          <div>
            <div className="flex justify-between items-center dark:text-gray-300">
              <div className="flex items-center">
                <LampIcon color="#707070" />
                <span className="ml-2">亮度</span>
              </div>
              <span className="font-medium">100%</span>
            </div>
            <Slider
              className="!m-0"
              defaultValue={30}
              tooltip={{ autoAdjustOverflow: true }}
              min={0}
              max={100}
            />
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <BatteryIcon color="#707070" />
              <span className="ml-2">电池容量(36Ah)</span>
            </div>
            <div className="flex items-center relative w-[140px]">
              <div className="flex-1 h-[13px] overflow-hidden rounded-full bg-gray-300">
                <div className="w-[80%] h-[13px] rounded-full bg-green-700 z-10" />
              </div>
              <span className="ml-2 text-sm">100%</span>
            </div>
          </div>
        </div>
        <div className="col-span-2 space-y-2 text-sm text-gray-500 relative">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>设备电流</span>
            </div>
            <span>2.1A</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>设备电压</span>
            </div>
            <span>24V</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <PowerIcon color="#707070" width={16} height={16} />
              <span>设备功率</span>
            </div>
            <span>24W</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <BatteryCharging className="h-4 w-4" />
              <span>蓄电池电压</span>
            </div>
            <span>24V</span>
          </div>
          {/* <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>今日放电</span>
            </div>
            <span>12AH-10%</span>
          </div> */}
          <div className="absolute right-[-16px] top-0 bottom-0 w-[1px] bg-gray-200 dark:bg-gray-600" />
        </div>
        <div className="col-span-2 space-y-2 text-sm text-gray-500 relative">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>充电电流</span>
            </div>
            <span>2.1A</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>充电电压</span>
            </div>
            <span>24V</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <PowerIcon color="#707070" width={16} height={16} />
              <span>充电功率</span>
            </div>
            <span>24W</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <BatteryCharging className="h-4 w-4" />
              <span>LVD/LVR</span>
            </div>
            <span>22.4/24.4</span>
          </div>
          {/* <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>今日充电</span>
            </div>
            <span>12AH-10%</span>
          </div> */}
          <div className="absolute right-[-16px] top-0 bottom-0 w-[1px] bg-gray-200 dark:bg-gray-600" />
        </div>
        <div className="col-span-2  text-gray-500 flex flex-col justify-between">
          <div className="w-full space-y-2 text-sm">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>太阳能策略</span>
              </div>
              <span>Test策略</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>GPS位置</span>
              </div>
              <span>成都市...</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <PowerIcon color="#707070" width={16} height={16} />
                <span>版本号</span>
              </div>
              <span>13.12</span>
            </div>
          </div>
          {/* <div className="flex w-full gap-2 justify-end items-end">
            <Button>读数据</Button>
            <Button>移动至道路</Button>
          </div> */}
        </div>
        <div className="flex flex-col col-span-2 justify-between items-end text-gray-500 text-sm">
          <div className="w-full space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>今日充电</span>
              </div>
              <span>12AH-10%</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>今日充电</span>
              </div>
              <span>12AH-10%</span>
            </div>
          </div>
          <div className="flex w-[23vw] gap-2 justify-end flex-wrap-reverse">
            <Button>更新数据</Button>
            <Button>移动至道路</Button>
            <Button>太阳能参数</Button>
          </div>
        </div>
      </div>
    );
}