import BatteryIcon from "@/assets/svgIcons/BatteryIcon";
import LampIcon from "@/assets/svgIcons/LampIcons";
import {CheckOutlined, CloseOutlined, DownOutlined, EditOutlined} from "@ant-design/icons";
import {Button, Divider, Input, Popover, Slider} from "antd";
import { useState } from "react";

export default function SimpleIotPage(props: Readonly<Record<string, any>>){
    const data = props;
    const DetailsIotInfo = (details: any) => {
        // console.log(details);
        return (
          <div className="p-2 w-[180px]">
            <div className="flex justify-between items-center">
              <h2 className="font-bold">设备详情</h2>
              <Button
                size="small"
                type="link"
                className="text-gray-500 text-sm"
                onClick={() => {
                  console.log('close');
                }}
              >
                刷新
              </Button>
            </div>
            <Divider className="!my-2" />
            <div className="flex justify-between items-center space-y-2 text-sm">
              <span>充电电流</span>
              <span>2.1A</span>
            </div>
            <div className="flex justify-between items-center">
              <span>充电电压</span>
              <span>24V</span>
            </div>
          </div>
        );
    }
    const [isDeviceInput, setIsDeviceInput] = useState<boolean>(false);
    const [deviceName, setDeviceName] = useState<string>('');
    const SaveIotDeviceName = () => {
        setIsDeviceInput(false);
        console.log(deviceName);
    }
    return (
        <div className="w-full min-w-[400px] h-fit card-base rounded-lg p-4 text-gray-800 dark:text-white dark:bg-gray-800">
          <div className="w-full rounded-lg flex items-center justify-between">
              <div className={'flex justify-start items-center gap-2'}>
                  {
                      isDeviceInput ? (
                          <Input value={deviceName} onChange={(e) => setDeviceName(e.target.value)} placeholder="" variant="underlined" className={'!font-bold !h-[24px]'} autoFocus onPressEnter={SaveIotDeviceName}/>
                      ) : (
                          <span className={'font-bold'}>设备详情</span>
                      )
                  }
                  {isDeviceInput ? (
                      <div className={'flex gap-2'}>
                          <CheckOutlined className={'!text-gray-400 !text-sm'} onClick={SaveIotDeviceName}/>
                          <CloseOutlined className={'!text-gray-400 !text-sm'} onClick={() => setIsDeviceInput(false)}/>
                      </div>
                  ) : (
                      <EditOutlined
                          className={'!text-gray-400 !text-sm'}
                          onClick={() => setIsDeviceInput(true)}
                      />
                  ) }
              </div>
            <span className="text-gray-500">在线</span>
          </div>
          <div className="my-1">
            <span className="text-gray-500 text-sm">SN:BE234NGD212</span>
          </div>
          <div>
            <div className="flex justify-between items-center dark:text-gray-300">
              <div className="flex items-center">
                <LampIcon color="#707070" />
                <span className="ml-2">亮度</span>
              </div>
              <span className="font-medium">100%</span>
            </div>
            <Slider
              className="!m-0"
              defaultValue={30}
              tooltip={{ autoAdjustOverflow: true }}
              min={0}
              max={100}
            />
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <BatteryIcon color="#707070" />
              <span className="ml-2">电池容量(36Ah)</span>
            </div>
            <div className="flex items-center relative w-[140px]">
              <div className="flex-1 h-[13px] overflow-hidden rounded-full bg-gray-300">
                <div className="w-[80%] h-[13px] rounded-full bg-green-700 z-10" />
              </div>
              <span className="ml-2 text-sm">100%</span>
            </div>
          </div>
          <Popover content={DetailsIotInfo({data:''})} trigger="click">
              <Button className="w-full mt-2 !justify-between">
              <span className="text-sm/loose text-gray-950 font-[550] dark:text-gray-300">
                  查看更多详细信息
              </span>
              <DownOutlined
                  style={{ fontSize: '12px', color: 'var(--color-gray-500)' }}
              />
              </Button>
          </Popover>
        </div>
    );
}