import React, { useState, useEffect, useRef } from 'react';
import './Typewriter.css'

interface TypewriterProps {
    texts: [string, string];
    speed?: number;
}

export const Typewriter: React.FC<TypewriterProps> = ({ texts, speed = 100 }) => {
    const [firstLine, setFirstLine] = useState('');
    const [secondLine, setSecondLine] = useState('');
    const [currentPhase, setCurrentPhase] = useState<0 | 1 | 2>(0);
    const underlineRef = useRef<HTMLSpanElement>(null);
    const cursorRef = useRef<HTMLSpanElement>(null);

    useEffect(() => {
        let interval: any;

        if (currentPhase === 0) {
            let index = 0;
            interval = setInterval(() => {
                if (index < texts[0].length) {
                    setFirstLine(texts[0].slice(0, index + 1));
                    index++;
                } else {
                    clearInterval(interval);
                    setCurrentPhase(1);
                }
            }, speed);
        } else if (currentPhase === 1) {
            // 显示下划线后切换到第二阶段
            setTimeout(() => {
                setCurrentPhase(2);
            }, 500);
        } else if (currentPhase === 2) {
            // 打印第二行
            let index = 0;
            interval = setInterval(() => {
                if (index < texts[1].length) {
                    setSecondLine(texts[1].slice(0, index + 1));
                    index++;
                } else {
                    clearInterval(interval);
                    if (cursorRef.current) {
                        cursorRef.current.style.animation = 'blink 1s infinite';
                    }
                }
            }, speed);
        }

        return () => clearInterval(interval);
    }, [currentPhase, speed, texts]);

    return (
        <div className={"typewriter_container"}>
            <div className={'text-3xl ml-4 text-red-600'}>
                {firstLine}
                <span
                    ref={cursorRef}
                    style={{
                        opacity: currentPhase === 0 ? 1 : 0,
                        marginLeft: '2px',
                        borderRight: '2px solid currentColor'
                    }}
                />
                <br />
                {currentPhase >= 1 && (
                    <span
                        ref={underlineRef}
                        style={{
                            display: 'block',
                            width: '255px',
                            height: '2px',
                            marginTop: '4px',
                            background: 'currentColor',
                            animation: 'underline 0.5s ease-out'
                        }}
                    />
                )}
            </div>
            <div style={{ marginTop: '8px' }} className={'text-md ml-4 text-white'}>
                {secondLine}
                <span
                    ref={cursorRef}
                    style={{
                        opacity: currentPhase === 2 ? 1 : 0,
                        marginLeft: '2px',
                        borderRight: '2px solid currentColor'
                    }}
                />
            </div>

            <style>{`
                @keyframes underline {
                    from { transform: scaleX(0) }
                    to { transform: scaleX(1) }
                }
                @keyframes blink {
                    0%, 100% { opacity: 1 }
                    50% { opacity: 0 }
                }
            `}</style>
        </div>
    );
};
