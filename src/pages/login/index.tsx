import { useTranslation } from "react-i18next";
import i18n from "@/locales/i18n";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import { Button, Checkbox, type CheckboxProps, Input, message } from "antd";
import style from "./index.module.less";
import type React from "react";
import {useEffect, useState, useRef} from "react";
import { User } from "@icon-park/react";
import { createStyles } from "antd-style";
import classNames from "classnames";
import {Typewriter} from "@/pages/login/Typewriter.tsx";

export default function LoginPage() {
  const { t } = useTranslation();
  const [username, setUsername] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isFocusedPwd, setIsFocusedPwd] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();

  const useStyle = createStyles(({ prefixCls, css }) => ({
    linearGradientButton: css`
      &.${prefixCls}-btn-primary:not([disabled]):not(.${prefixCls}-btn-dangerous) {
        > span {
          position: relative;
        }

        &::before {
          content: "";
          background: linear-gradient(135deg, #6253e1, #04befe);
          position: absolute;
          inset: -1px;
          opacity: 1;
          transition: all 0.3s;
          border-radius: inherit;
        }

        &:hover::before {
          opacity: 0;
        }
      }
    `,
  }));
  const { styles } = useStyle();
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => {
    if (username.trim() === "") {
      setIsFocused(false);
    }
  };
  const pwdFocus = () => setIsFocusedPwd(true);
  const pwdBlur = () => {
    if (password.trim() === "") {
      setIsFocusedPwd(false);
    }
  };
  const inputChange = (e: any, type: string) => {
    if (type === "username") {
      setUsername(e.target.value);
    } else {
      setPassword(e.target.value);
    }
  };

  const [selectLanguage, setSelectLanguage] = useState<string>("chinese");
  useEffect(() => {
    const language = localStorage.getItem("language");
    if (language) {
      selectLanguageChange(language);
    }
  }, []);
  const selectLanguageChange = (l: string) => {
    setSelectLanguage(l);
    localStorage.setItem("language", l);
    switch (l) {
      case "chinese":
        i18n.changeLanguage("zh");
        break;
      case "English":
        i18n.changeLanguage("en");
        break;
      default:
        i18n.changeLanguage('en');
        break;
    }
  };
  const [loading, setLoading] = useState<boolean>(false);
  const signIn = () => {
    console.log(username, password);
    setLoading(true);
    if (checked) {
      localStorage.setItem("password", password);
      setUserErr(true)
      setPwdErr(true)
    }


    messageApi.open({
      type: "error",
      content: "登录失败，请检查用户名和密码！",
      style: {
        marginTop: "20vh",
      },
    });
  };
  const languageRefs: Record<string, any> = {
    chinese: useRef(null),
    English: useRef(null),
  };
  const [underlineStyle, setUnderlineStyle] = useState<React.CSSProperties>({});
  useEffect(() => {
    const selectedRef = languageRefs[selectLanguage];
    if (selectedRef.current) {
      setUnderlineStyle({
        transform: `translateX(${selectedRef.current.offsetLeft}px)`,
        width: `${selectedRef.current.offsetWidth}px`,
      });
    }
  }, [selectLanguage]);

  const [checked, setChecked] = useState<boolean>(false);
  const [userErr, setUserErr] = useState<boolean>(false);
  const [pwdErr, setPwdErr] = useState<boolean>(false);
  const onChangeCheck: CheckboxProps["onChange"] = (e) => {
    setChecked(e.target.checked);
  };
  return (
    <div className={style.loginPage}>
      <div className={style.logInBox}>
        <div className={classNames(style.loginTitle, "bg-blue-500 pt-[130px]")}>
          <Typewriter  texts={["iNET for Smart City", "IoT Based Lighting Management System."]} />
          <div className={style.language}>
            {/* biome-ignore lint/complexity/useLiteralKeys: <explanation> */}
            <div ref={languageRefs["chinese"]} onClick={() => selectLanguageChange("chinese")}>
              中文
            </div>
            <div className={style.separated}>|</div>
            {/* biome-ignore lint/complexity/useLiteralKeys: <explanation> */}
            <div ref={languageRefs["English"]} onClick={() => selectLanguageChange("English")}>
              English
            </div>
            <div className={style.underline} style={underlineStyle} />
          </div>
            <div className="combined">
              <div className="combined-shape">
                <div className="shape-left">
                </div>
                <div className="shape-right">
                </div>
              </div>
            </div>
        </div>
        <div className={classNames(style.loginInfo, "bg-gray-50 dark:bg-gray-950")}>
          <div className={style.loginForm}>
            <div className={classNames(style.loginTitleFrom, "text-gray-950 dark:text-gray-200")}>
              <div>E-LiTE</div>
              <div>E-LiTE Semiconductor Inc.</div>
            </div>
            <div className={`${style.loginContent} bg-white dark:bg-gray-950 ${isFocused ? style.inputFocused : ""}`}>
              <Input
                onFocus={handleFocus}
                onBlur={handleBlur}
                status={userErr ? "error" : ""}
                onChange={(e) => inputChange(e, "username")}
                placeholder=""
                suffix={<User theme="outline" size="16" fill="#b1a5a5" />}
              />
              {/* biome-ignore lint/a11y/noLabelWithoutControl: <explanation> */}
              <label className={classNames(style.loginLabel, isFocused ? "!bg-gray-50 dark:!bg-gray-950 text-gray-700 dark:text-gray-100" : "text-gray-500 dark:text-gray-400")}>
                {t("login.username")}
              </label>
            </div>
            <div style={{ marginTop: "20px" }} className={`${style.loginContent} bg-white dark:bg-gray-950 ${isFocusedPwd ? style.inputFocused : ""}`}>
              <Input.Password
                onFocus={pwdFocus}
                onBlur={pwdBlur}
                status={pwdErr ? "error" : ""}
                onChange={(e) => inputChange(e, "password")}
                placeholder=""
                onPressEnter={signIn}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
              {/* biome-ignore lint/a11y/noLabelWithoutControl: <explanation> */}
              <label className={classNames(style.loginLabel, isFocusedPwd ? "!bg-gray-50 dark:!bg-gray-900 text-gray-700 dark:text-gray-100" : "text-gray-500 dark:text-gray-400")}>
                {t("login.password")}
              </label>
            </div>
            <div className={"flex justify-start mt-2 "}>
              <Checkbox onChange={onChangeCheck} className={"text-gray-500 dark:text-gray-300"}>
                {t("login.remember")}
              </Checkbox>
            </div>
          </div>
          <Button className={styles.linearGradientButton} onClick={signIn} type="primary" size="middle" style={{ marginTop: "30px", width: "220px" }} loading={loading}>
            {t("login.login")}
          </Button>
          {contextHolder}
        </div>
      </div>
    </div>
  );
}
