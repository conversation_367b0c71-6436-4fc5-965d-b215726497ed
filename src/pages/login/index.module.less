@import "../../assets/fonts/index.less";

.loginPage {
  user-select: none; /* Safari */
  width: 100%;
  height: 100%;
  background-image: url("../../assets/login.jpg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .logInBox {
    width: 700px;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    background-image: url("@/assets/texture.png");
    display: flex;

    .loginTitle {
      background-image: url("@/assets/texture.png");
      width: 50%;
      height: 100%;
      position: relative;

      .language {
        position: absolute;
        bottom: 12px;
        right: 24px;
        color: #fff;
        cursor: pointer;
        display: flex;
      }

      .separated {
        margin: 0 5px;
      }
    }

    .loginInfo {
      width: 50%;
      height: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;

      .loginForm {
        width: auto;
        height: auto;
      }

      .loginTitleFrom {
        text-align: center;
        align-items: center;
        justify-content: center;
        display: flex;
        flex-direction: column;
        height: 150px;
        font-family: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;

        > div:first-child {
          font-size: 30px;
          color: red;
        }
      }
    }
  }

  .loginContent {
    width: 220px;
    height: auto;
    position: relative;

    .loginLabel {
      position: absolute;
      top: -4px;
      z-index: 999;
      pointer-events: none;
      background-color: transparent;
      left: 15px;
      transform: translateY(0.6rem);
      transition: all 0.3s ease;
    }
  }

  .inputFocused .loginLabel {
    transform: translateY(-31%) translateX(-5px) scale(0.8);
    padding: 0;
    font-weight: bold;
    letter-spacing: 1px;
    border: none;
    border-radius: 2px;
    user-select: none; /* Safari */
  }
}

.underline {
  position: absolute;
  height: 1px;
  bottom: -4px;
  background-color: #fff;
  transition: transform 0.3s ease-in-out;
}
//font-family: AlimamaDaoLiTi, Arial ,monospace;

.typewriter_container {
  font-family: AlimamaDaoLiTi, Arial ,monospace;
  line-height: 1.8;
  white-space: pre-wrap;
}

.current_line, .completed_line {
  display: inline-block;
  position: relative;
}

.cursor {
  position: absolute;
  margin-left: 2px;
  animation: cursor-blink 1.2s step-end infinite;
}

@keyframes cursor_blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 保证字符等宽 */
.completed_line, .current_line {
  font-family: monospace;
}