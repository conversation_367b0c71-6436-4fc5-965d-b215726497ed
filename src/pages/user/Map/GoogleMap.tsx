// biome-ignore lint/suspicious/noShadowRestrictedNames: <explanation>
import { APIProvider, Map, AdvancedMarker, useMap, useMapsLibrary, InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps';
import { useEffect, useState, useMemo } from 'react';
import { Input } from 'antd';
import { useTranslation } from 'react-i18next';
export function GoogleMapProject() {
    const apiKey = import.meta.env.VITE_BASE_GOOGLE_MAP_KEY;
    const [mapError, setMapError] = useState<string>('');
    const [position, setPosition] = useState({ lat: 22.54992, lng: 113.94924 });
    const [searchValue, setSearchValue] = useState('');
    const [address, setAddress] = useState('');
    const { t } = useTranslation();

    // 加载地理编码库
    const geocodingLib = useMapsLibrary('geocoding');
    const geocoder = useMemo(
        () => geocodingLib && new geocodingLib.Geocoder(),
        [geocodingLib]
    );
    const [initialize, setInitialize] = useState({ lat: 22.54992, lng: 113.94924 });
    // 获取当前位置
    useEffect(() => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    setPosition({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    });
                    setInitialize({
                      lat: position.coords.latitude,
                      lng: position.coords.longitude,
                    });
                    // 反向地理编码获取地址
                    if (geocoder) {
                        geocoder.geocode({
                            location: {
                                lat: position.coords.latitude,
                                lng: position.coords.longitude
                            }
                        }).then((response) => {
                            if (response.results[0]) {
                                setAddress(response.results[0].formatted_address);
                                setSearchValue(response.results[0].formatted_address);
                            }
                        }).catch((error) => {
                            console.error('Geocoder failed:', error);
                        });
                    }
                },
                (error) => {
                    console.error('Error getting location:', error);
                }
            );
        }
    }, [geocoder]);

    // 搜索地址
    const handleSearch = () => {
        if (geocoder && searchValue) {
            geocoder.geocode({ address: searchValue })
                .then((response) => {
                    if (response.results[0]) {
                        const { lat, lng } = response.results[0].geometry.location;
                        setPosition({ lat: lat(), lng: lng() });
                        setAddress(response.results[0].formatted_address);
                    }
                })
                .catch((error) => {
                    console.error('Geocoding error:', error);
                });
        }
    };

    // 处理标记拖动
    const handleMarkerDrag = (e: google.maps.MapMouseEvent) => {
        if (e.latLng) {
            const newPos = { lat: e.latLng.lat(), lng: e.latLng.lng() };
            setPosition(newPos);
            
            // 反向地理编码获取新地址
            if (geocoder) {
                geocoder.geocode({ location: newPos })
                    .then((response) => {
                        if (response.results[0]) {
                            setAddress(response.results[0].formatted_address);
                            setSearchValue(response.results[0].formatted_address);
                        }
                    })
                    .catch((error) => {
                        console.error('Reverse geocoding error:', error);
                    });
            }
        }
    };

    const handleError = (error: unknown) => {
        console.error('Google Maps API error:', error);
        if (error && typeof error === 'object' && 'message' in error) {
            setMapError(error.message as string);
        } else {
            setMapError('An error occurred while loading Google Maps');
        }
    };
    const [markerRef, marker] = useAdvancedMarkerRef();

  return (
    <APIProvider apiKey={apiKey} onError={handleError}>
      <div className="relative w-full h-full">
        <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-lg p-2 h-fit w-96 max-w-[calc(100%-2rem)]">
          <Input.Search
            placeholder={t('search_location')}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onSearch={handleSearch}
            className=""
          />
          {address && (
            <div className="text-sm text-gray-600">
              <div>Lat: {position.lat.toFixed(6)}</div>
              <div>Lng: {position.lng.toFixed(6)}</div>
              <div className="truncate">{address}</div>
            </div>
          )}
        </div>
        <Map
          id="map"
          style={{ width: '100%', height: '100%' }}
          defaultCenter={initialize}
          defaultZoom={20}
          disableDefaultUI={false}
          fullscreenControl={false}
          streetViewControl={false}
          zoomControl={true}
          mapTypeControl={false}
          mapId={'8f541b0616c0b3e'}
          gestureHandling={'greedy'}
        >
          <AdvancedMarker
            position={position}
            draggable={true}
            onDragEnd={handleMarkerDrag}
            ref={markerRef}
          />
          <InfoWindow anchor={marker} headerDisabled={true}>
            <div className="py-2 text-gray-900">
              <div className="font-bold">{t('地址')}：</div>
              <div className="truncate">{address}</div>
              <div className="font-bold mt-2">
                Lat: {position.lat.toFixed(6)}，Lng:
                {position.lng.toFixed(6)}
              </div>
            </div>
          </InfoWindow>
        </Map>
      </div>
    </APIProvider>
  );
}