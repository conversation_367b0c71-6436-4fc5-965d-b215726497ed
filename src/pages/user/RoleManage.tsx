import { useState } from 'react';
import { Button, Input, Tag, Space, Tooltip, Popconfirm, Divider, Popover, Checkbox, Modal, Form, Select, Tree, type TreeProps } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, RedoOutlined, AppstoreAddOutlined, SettingOutlined } from '@ant-design/icons';
import type { ColumnsType, ColumnType } from 'antd/es/table';
import TableSelected from '@/components/TableSelected';
import classNames from 'classnames';

interface RoleData {
  key: string;
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  createTime: string;
  permissions: number;
  permissionChars: string;
  assignedUsers: number;
}

export function RoleManage() {
  const [searchText, setSearchText] = useState('');
  const [searchStatus, setSearchStatus] = useState<string|null>(null);
  const [isRotating, setIsRotating] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<RoleData | null>(null);
  const [editForm] = Form.useForm();

  const [isUserListModalOpen, setIsUserListModalOpen] = useState(false);
  const [selectedRoleUsers, setSelectedRoleUsers] = useState<{name: string; account: string}[]>([]);
  const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  // 模拟权限树状数据
  const permissionTreeData: any[] = [
    {
      title: '0-0',
      key: '0-0',
      children: [
        {
          title: '0-0-0',
          key: '0-0-0',
          children: [
            { title: '0-0-0-0', key: '0-0-0-0' },
            { title: '0-0-0-1', key: '0-0-0-1' },
            { title: '0-0-0-2', key: '0-0-0-2' },
          ],
        },
        {
          title: '0-0-1',
          key: '0-0-1',
          children: [
            { title: '0-0-1-0', key: '0-0-1-0' },
            { title: '0-0-1-1', key: '0-0-1-1' },
            { title: '0-0-1-2', key: '0-0-1-2' },
          ],
        },
        {
          title: '0-0-2',
          key: '0-0-2',
        },
      ],
    },
    {
      title: '0-1',
      key: '0-1',
      children: [
        { title: '0-1-0-0', key: '0-1-0-0' },
        { title: '0-1-0-1', key: '0-1-0-1' },
        { title: '0-1-0-2', key: '0-1-0-2' },
      ],
    },
    {
      title: '0-2',
      key: '0-2',
    },
  ];

  const handlePermissionModalOpen = (record: RoleData) => {
    setSelectedRole(record);
    // 这里可以根据实际情况设置已选中的权限
    setSelectedPermissions([]);
    setIsPermissionModalOpen(true);
  };

  const handlePermissionModalOk = () => {
    console.log('保存权限设置:', selectedRole?.id, selectedPermissions);
    setIsPermissionModalOpen(false);
  };

  const handlePermissionModalCancel = () => {
    setIsPermissionModalOpen(false);
  };


  const columns: ColumnsType<RoleData> = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '权限字符',
      dataIndex: 'permissionChars',
      key: 'permissionChars',
    },
    {
      title: '角色状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'success' : 'error'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '已分配用户',
      dataIndex: 'assignedUsers',
      key: 'assignedUsers',
      render: (users, record) => (
        <Button
          type="link"
          onClick={() => handleViewUsers(record)}
        >
          {users} 个用户
        </Button>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此角色吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="权限分配">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handlePermissionModalOpen(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 模拟数据
  const handleViewUsers = (record: RoleData) => {
    // 模拟获取用户列表数据
    const mockUsers = [
      { name: '张三', account: 'zhangsan' },
      { name: '李四', account: 'lisi' },
    ];
    setSelectedRoleUsers(mockUsers);
    setIsUserListModalOpen(true);
  };

  const handleUserListModalClose = () => {
    setIsUserListModalOpen(false);
  };

  const data: RoleData[] = [
    {
      key: '1',
      id: '1',
      name: '超级管理员',
      description: '系统最高权限角色',
      status: 'active',
      permissions: 10,
      createTime: '2024-03-20',
      permissionChars: 'admin',
      assignedUsers: 2,
    },
    {
      key: '2',
      id: '2',
      name: '普通用户',
      description: '基础功能权限角色',
      status: 'active',
      permissions: 5,
      createTime: '2024-03-20',
      permissionChars: 'user',
      assignedUsers: 3,
    },
  ];

  const handleAdd = () => {
    // 处理添加角色逻辑
    console.log('添加角色');
  };

  const handleEdit = (record: RoleData) => {
    setSelectedRole(record);
    editForm.setFieldsValue(record);
    setIsEditModalOpen(true);
  };

  const handleEditModalOk = () => {
    editForm.validateFields().then((values) => {
      console.log('编辑角色:', values);
      setIsEditModalOpen(false);
      editForm.resetFields();
    });
  };

  const handleEditModalCancel = () => {
    setIsEditModalOpen(false);
    editForm.resetFields();
  };

  const handleDelete = (record: RoleData) => {
    // 处理删除角色逻辑
    console.log('删除角色', record);
  };

  const handleBatchDelete = () => {
    // 处理批量删除角色逻辑
    console.log('批量删除角色');
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    // 处理搜索逻辑
    console.log('搜索:', value);
  };

  const handleStatusChange = (value: string) => {
    setSearchStatus(value);
    // 处理状态筛选逻辑
    console.log('状态筛选:', value);
  };

  const defaultCheckedList = columns.map((item) => item.key);
  const [checkedList, setCheckedList] = useState(defaultCheckedList);
  const CheckboxOptions = columns.map(({ key, title }) => ({
    label: title as React.ReactNode,
    value: key,
  }));

  const TableListShow = (
    <div className="flex flex-col justify-center items-center cursor-default">
      <Checkbox.Group
        className="flex flex-col gap-1"
        value={checkedList}
        options={CheckboxOptions}
        onChange={(value) => {
          setCheckedList(value as string[]);
        }}
      />
    </div>
  );


  // 权限树
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([
    '0-0-0',
    '0-0-1',
  ]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>(['0-0-0']);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  // const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  // const onExpand: TreeProps['onExpand'] = (expandedKeysValue) => {
  //   console.log('onExpand', expandedKeysValue);
  //   // if not set autoExpandParent to false, if children expanded, parent can not collapse.
  //   // or, you can remove all expanded children keys.
  //   setExpandedKeys(expandedKeysValue);
  //   setAutoExpandParent(false);
  // };

  const onCheck: TreeProps['onCheck'] = (checkedKeysValue) => {
    console.log('onCheck', checkedKeysValue);
    setCheckedKeys(checkedKeysValue as React.Key[]);
  };

  const onSelect: TreeProps['onSelect'] = (selectedKeysValue, info) => {
    console.log('onSelect', info);
    setSelectedKeys(selectedKeysValue);
  };

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <div className="flex">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            className="mr-2"
          >
            新增角色
          </Button>
          <Button
            type="dashed"
            icon={<DeleteOutlined />}
            onClick={handleBatchDelete}
            danger
          >
            批量删除
          </Button>
        </div>
        <div className="flex gap-2">
          <Input.Search
            placeholder="请输入搜索内容"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={handleSearch}
          />
          <Select
            placeholder="角色状态"
            allowClear
            style={{ width: 120 }}
            value={searchStatus}
            onChange={handleStatusChange}
            options={[
              { value: 'active', label: '启用' },
              { value: 'inactive', label: '停用' },
            ]}
          />
          <div className="card-base rounded-md bg-gray-200 dark:bg-gray-800 flex items-center gap-1">
            <button
              type="button"
              className={classNames(
                'w-8 h-[30px] flex items-center justify-center',
                { 'animate-spin': isRotating },
              )}
              onClick={() => {
                setIsRotating(true);
                setTimeout(() => setIsRotating(false), 1000);
              }}
            >
              <RedoOutlined className="!text-gray-900 dark:!text-gray-300" />
            </button>
            <Divider type="vertical" className="!mx-[0]" />
            <Popover
              placement="bottomRight"
              content={TableListShow}
              arrow={false}
              forceRender={true}
            >
              <div className="w-8 h-[30px] flex items-center justify-center">
                <AppstoreAddOutlined className="!text-gray-800 dark:!text-gray-300" />
              </div>
            </Popover>
          </div>
        </div>
      </div>
      <div className="h-[calc(100%-50px)]">
        <TableSelected<RoleData>
          columns={columns as ColumnType<RoleData>[]}
          dataSource={data}
          total={2}
          callbackOnRows={(rows) => {
            console.log(rows);
          }}
          callbackPages={() => {
            // 处理分页变化
          }}
          tableProps={{}}
        />
      </div>

      <Modal
        title="编辑角色信息"
        open={isEditModalOpen}
        onOk={handleEditModalOk}
        onCancel={handleEditModalCancel}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          initialValues={selectedRole || {}}
        >
          <Form.Item
            name="name"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入角色描述' }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择角色状态' }]}
          >
            <Select
              options={[
                { value: 'active', label: '启用' },
                { value: 'inactive', label: '停用' },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="permissionChars"
            label="权限字符"
            rules={[{ required: true, message: '请输入权限字符' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="权限分配"
        open={isPermissionModalOpen}
        onOk={handlePermissionModalOk}
        onCancel={handlePermissionModalCancel}
        width={500}
        styles={{ body: { height: '50vh', padding: '12px' } }}
      >
        <div className="h-full overflow-y-auto pr-2">
          <Tree
            checkable
            showLine
            autoExpandParent
            defaultExpandParent
            defaultExpandAll={true}
            onCheck={onCheck}
            checkedKeys={checkedKeys}
            onSelect={onSelect}
            selectedKeys={selectedKeys}
            treeData={permissionTreeData}
          />
        </div>
      </Modal>

      <Modal
        title="已分配用户列表"
        open={isUserListModalOpen}
        onCancel={handleUserListModalClose}
        footer={null}
        width={500}
      >
        <div className="max-h-[400px] overflow-y-auto">
          {selectedRoleUsers.map((user, index) => (
            <div
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              key={index}
              className="p-3 border-b border-gray-200 last:border-0 flex justify-between items-center"
            >
              <span>{user.name}</span>
              <span className="text-gray-500">{user.account}</span>
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
}