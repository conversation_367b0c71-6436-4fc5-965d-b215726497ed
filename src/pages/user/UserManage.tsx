import { useState } from 'react';
import { Button, Input, Avatar, Tag, Space, Tooltip, Popconfirm, Divider, Popover, Checkbox, type CheckboxOptionType, Modal, Form, Select, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LinkOutlined, RedoOutlined, AppstoreAddOutlined, KeyOutlined } from '@ant-design/icons';
import type { ColumnsType, ColumnType } from 'antd/es/table';
import TableSelected from '@/components/TableSelected';
import classNames from 'classnames';

interface ProjectData {
  id: string;
  name: string;
  description: string;
}

interface UserData {
  key: string;
  id: string;
  account: string;
  avatar: string;
  name: string;
  role: string;
  phone: string;
  email: string;
  status: 'active' | 'inactive';
  alarm: boolean;
  supervisor: string;
  company: string;
  projects: number;
  projectList?: string[];
  createTime: string;
}

export default function UserManage() {
  const [searchText, setSearchText] = useState('');
  const [isRotating, setIsRotating] = useState(false);
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [editForm] = Form.useForm();
  const [passwordForm] = Form.useForm();

  // 模拟项目数据
  const projectList: ProjectData[] = [
    { id: '1', name: '项目A', description: '这是项目A的描述' },
    { id: '2', name: '项目B', description: '这是项目B的描述' },
    { id: '3', name: '项目C', description: '这是项目C的描述' },
    { id: '4', name: '项目D', description: '这是项目D的描述' },
  ];

  const handleProjectModalOpen = (record: UserData) => {
    setSelectedUser(record);
    setSelectedProjects(record.projectList || []);
    setIsProjectModalOpen(true);
  };

  const handleProjectModalOk = () => {
    if (selectedUser) {
      // 这里处理保存项目绑定的逻辑
      console.log('保存项目绑定:', selectedUser.id, selectedProjects);
    }
    setIsProjectModalOpen(false);
  };

  const handleProjectModalCancel = () => {
    setIsProjectModalOpen(false);
  };

  const handleProjectChange = (checkedValues: string[]) => {
    setSelectedProjects(checkedValues);
  };

  const columns: ColumnsType<UserData> = [
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      render: (avatar) => <Avatar src={avatar} />,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'success' : 'error'}>
          {status === 'active' ? '活跃' : '停用'}
        </Tag>
      ),
    },
    {
      title: '报警',
      dataIndex: 'alarm',
      key: 'alarm',
      render: (alarm) => (
        <Tag color={alarm ? 'warning' : 'default'}>
          {alarm ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '上级',
      dataIndex: 'supervisor',
      key: 'supervisor',
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
    },
    {
      title: '绑定项目',
      dataIndex: 'projects',
      key: 'projects',
      render: (projects, record) => (
        <Button
          type="link"
          onClick={() => handleProjectModalOpen(record)}
          icon={<LinkOutlined />}
        >
          {projects} 个项目
        </Button>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="修改密码">
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => handlePasswordChange(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此用户吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 模拟角色数据
  const roleOptions = [
    { value: 'admin', label: '管理员' },
    { value: 'user', label: '普通用户' },
    { value: 'guest', label: '访客' },
  ];

  // 模拟数据
  const data: UserData[] = [
    {
      key: '1',
      id: '1',
      account: 'john_doe',
      avatar: 'https://xsgames.co/randomusers/a vatar.php?g=pixel&key=1',
      name: '张三',
      role: '管理员',
      phone: '***********',
      email: '<EMAIL>',
      status: 'active',
      alarm: true,
      supervisor: '李四',
      company: '智能科技有限公司',
      projects: 3,
      createTime: '2024-03-20',
    },
    // 可以添加更多模拟数据
  ];

  const handleAdd = () => {
    // 处理添加用户逻辑
    console.log('添加用户');
  };

  const handleEdit = (record: UserData) => {
    setSelectedUser(record);
    editForm.setFieldsValue(record);
    setIsEditModalOpen(true);
  };

  const handleEditModalOk = () => {
    editForm.validateFields().then((values) => {
      console.log('编辑用户:', values);
      setIsEditModalOpen(false);
      editForm.resetFields();
    });
  };

  const handleEditModalCancel = () => {
    setIsEditModalOpen(false);
    editForm.resetFields();
  };

  const handlePasswordChange = (record: UserData) => {
    setSelectedUser(record);
    setIsPasswordModalOpen(true);
  };

  const handlePasswordModalOk = () => {
    passwordForm.validateFields().then((values) => {
      console.log('修改密码:', values);
      setIsPasswordModalOpen(false);
      passwordForm.resetFields();
    });
  };

  const handlePasswordModalCancel = () => {
    setIsPasswordModalOpen(false);
    passwordForm.resetFields();
  };

  const handleDelete = (record: UserData) => {
    // 处理删除用户逻辑
    console.log('删除用户', record);
  };
  const handleBatchDelete = () => {
    // 处理批量删除用户逻辑
    console.log('批量删除用户');
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    // 处理搜索逻辑
    console.log('搜索:', value);
  };
  const defaultCheckedList = columns.map((item) => item.key);
  const [checkedList, setCheckedList] = useState(defaultCheckedList);
  const CheckboxOptions = columns.map(({ key, title }) => ({
    label: title,
    value: key,
  }));
const TableListShow = (
  <div className="flex flex-col justify-center items-center  cursor-default">
    <Checkbox.Group
      className="flex flex-col gap-1 "
      value={checkedList}
      options={CheckboxOptions as CheckboxOptionType[]}
      onChange={(value) => {
        setCheckedList(value as string[]);
      }}
    />
  </div>
)
  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <div className="flex">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            className="mr-2"
          >
            新增用户
          </Button>
          <Button
            type="dashed"
            icon={<DeleteOutlined />}
            onClick={handleBatchDelete}
            danger
          >
            批量删除
          </Button>
        </div>
        <div className="flex">
          <Input.Search
            placeholder="请输入搜索内容"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={handleSearch}
          />
          <div className="card-base ml-2 rounded-md bg-gray-200 dark:bg-gray-800 flex items-center gap-1">
            <button
              type="button"
              className={classNames(
                'w-8 h-[30px] flex items-center justify-center',
                { 'animate-spin': isRotating },
              )}
              onClick={() => {
                setIsRotating(true);
                setTimeout(() => setIsRotating(false), 1000);
              }}
            >
              <RedoOutlined className="!text-gray-900 dark:!text-gray-300" />
            </button>
            <Divider type="vertical" className="!mx-[0]" />
            <Popover
              placement="bottomRight"
              content={TableListShow}
              arrow={false}
              forceRender={true}
            >
              <div className="w-8 h-[30px] flex items-center justify-center">
                <AppstoreAddOutlined className="!text-gray-800 dark:!text-gray-300" />
              </div>
            </Popover>

          </div>
        </div>
      </div>
      <div className="h-[calc(100%-50px)]">
        <TableSelected<UserData>
          columns={columns as ColumnType<UserData>[]}
          dataSource={data}
          total={1}
          callbackOnRows={(rows) => {
            console.log(rows);
          }}
          callbackPages={() => {
            // 处理分页变化
          }}
          tableProps={{}}
        />
      </div>
      <Modal
        title="绑定项目"
        open={isProjectModalOpen}
        onOk={handleProjectModalOk}
        onCancel={handleProjectModalCancel}
        width={500}
      >
        <div className="max-h-[400px] overflow-y-auto">
          <Checkbox.Group
            className="flex flex-col gap-2"
            value={selectedProjects}
            onChange={handleProjectChange}
          >
            {projectList.map((project) => (
              <Checkbox key={project.id} value={project.id}>
                <div className="flex flex-col">
                  <span className="font-medium">{project.name}</span>
                  <span className="text-sm text-gray-500">{project.description}</span>
                </div>
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      </Modal>

      <Modal
        title="编辑用户信息"
        open={isEditModalOpen}
        onOk={handleEditModalOk}
        onCancel={handleEditModalCancel}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          initialValues={selectedUser || {}}
        >
          <Form.Item
            name="account"
            label="账号"
            rules={[{ required: true, message: '请输入账号' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select options={roleOptions} />
          </Form.Item>
          <Form.Item
            name="phone"
            label="电话"
            rules={[{ required: true, message: '请输入电话' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label="邮箱"
            rules={[{ required: true, message: '请输入邮箱' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="supervisor"
            label="上级"
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="company"
            label="公司"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="alarm"
            label="报警"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="修改密码"
        open={isPasswordModalOpen}
        onOk={handlePasswordModalOk}
        onCancel={handlePasswordModalCancel}
        width={400}
      >
        <Form
          form={passwordForm}
          layout="vertical"
        >
          <Form.Item
            name="password"
            label="新密码"
            rules={[{ required: true, message: '请输入新密码' }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            label="确认密码"
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}