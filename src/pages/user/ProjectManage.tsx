import { ProjectAtom } from "@/jotai/atoms";
import { SearchOutlined } from "@ant-design/icons";
import { Input, Tree, Select, Radio, Form, type TreeDataNode } from "antd";
import { useAtom } from "jotai";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { GoogleMapProject } from "./Map/GoogleMap";

interface ProjectFormData {
  projectName: string;
  projectDescription: string;
  projectTimezone: string;
  mapType: 'google' | 'gaode';
  location: string;
  longitude: string;
  latitude: string;
}

export default function ProjectManage () {
  const [form] = Form.useForm<ProjectFormData>();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const { t } = useTranslation();
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [treeData, ] = useAtom(ProjectAtom);

  const onExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };
  const onSelectHandel = (
    keys: React.Key[],
    info: { node: { key: React.Key } },
  ) => {
    const clickedKey = info.node.key;
    if (selectedKeys.includes(clickedKey)) {
      return;
    }
    setSelectedKeys([clickedKey]);
    if (keys.length > 0) {
      setExpandedKeys(keys);
    }
    // 这里 后续需要根据数据进行调整，是否需要解析器来解析数据，获取pid，road—id，gid，zid
    setSelectedProject({ keys: keys, info: info });
  };
    // 这里获取TreeData的key和title组成数组
  const dataList: { key: React.Key; title: string }[] = [];
  const generateList = (data: TreeDataNode[]) => {
    for (const element of data) {
      const node = element;
      const { key, title } = node;
      dataList.push({ key, title: title as string });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(treeData);
  const getParentKey = (key: React.Key, tree: TreeDataNode[]): React.Key => {
    let parentKey: React.Key;
    for (const element of tree) {
      const node = element;
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    return parentKey!;
  };
    // 搜索功能
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const newExpandedKeys = dataList
      .map((item) => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, treeData);
        }
        return null;
      })
      .filter(
        (item, i, self): item is React.Key =>
          !!(item && self.indexOf(item) === i),
      );
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
  };
  const treeDataHandle = useMemo(() => {
    if (searchValue === '') {
      setExpandedKeys([]);
    }
    const loop = (data: TreeDataNode[]): TreeDataNode[] =>
      data.map((item) => {
        const strTitle = item.title as string;
        const index = strTitle.indexOf(searchValue);
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
          index > -1 ? (
            <span key={item.key}>
              {beforeStr}
              <span style={{ color: 'red' }}>{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span key={item.key}>{strTitle}</span>
          );
        if (item.children) {
          return { title, key: item.key, children: loop(item.children) };
        }

        return {
          title,
          key: item.key,
        };
      });

    return loop(treeData);
  }, [searchValue, treeData]);

  return (
    <div className="flex w-full h-full">
      <div className="card-base flex flex-col h-full w-[300px] rounded-lg p-2 mr-4">
        <Input
          className="mb-2"
          placeholder={t('search_project')}
          style={{ backgroundColor: 'transparent', width: '100%' }}
          allowClear
          onChange={onChange}
          suffix={<SearchOutlined />}
        />
        <Tree
          className="!flex-1"
          rootStyle={{ backgroundColor: 'transparent' }}
          expandedKeys={expandedKeys}
          selectedKeys={selectedKeys}
          onExpand={onExpand}
          autoExpandParent={true}
          showLine={true}
          blockNode={true}
          onSelect={onSelectHandel}
          treeData={treeDataHandle}
        />
      </div>
      <div className="card-base flex-1 rounded-lg p-4">
        <Form
          form={form}
          layout="vertical"
          className="flex flex-col gap-0"
          initialValues={{
            mapType: 'google',
          }}
          onFinish={(values: ProjectFormData) => {
            console.log('表单数据:', values);
          }}
        >
          <div className="flex flex-col gap-1">
            <h2 className="text-lg font-semibold">{t('basic_info')}</h2>
            <Form.Item
              name="projectName"
              label={t('project_name')}
              rules={[
                { required: true, message: t('project_name') + t('required') },
              ]}
            >
              <Input.TextArea autoSize={{ minRows: 1, maxRows: 1 }} />
            </Form.Item>
            <Form.Item
              name="projectDescription"
              label={t('project_description')}
              rules={[]}
            >
              <Input.TextArea autoSize={{ minRows: 2, maxRows: 4 }} />
            </Form.Item>
            <Form.Item
              className="!w-[300px]"
              name="projectTimezone"
              label={t('project_timezone')}
              rules={[
                {
                  required: true,
                  message: t('project_timezone') + t('required'),
                },
              ]}
            >
              <Select
                className="w-[300px]"
                options={[
                  {
                    value: 'UTC+12',
                    label: `UTC+12 (${t('timezone.wellington')})`,
                  },
                  {
                    value: 'UTC+10',
                    label: `UTC+10 (${t('timezone.sydney')})`,
                  },
                  { value: 'UTC+9', label: `UTC+9 (${t('timezone.tokyo')})` },
                  { value: 'UTC+8', label: `UTC+8 (${t('timezone.beijing')})` },
                  { value: 'UTC+7', label: `UTC+7 (${t('timezone.bangkok')})` },
                  { value: 'UTC+5', label: `UTC+5 (${t('timezone.karachi')})` },
                  { value: 'UTC+4', label: `UTC+4 (${t('timezone.dubai')})` },
                  { value: 'UTC+3', label: `UTC+3 (${t('timezone.moscow')})` },
                  { value: 'UTC+2', label: `UTC+2 (${t('timezone.cairo')})` },
                  { value: 'UTC+1', label: `UTC+1 (${t('timezone.paris')})` },
                  { value: 'UTC+0', label: `UTC+0 (${t('timezone.london')})` },
                  {
                    value: 'UTC-3',
                    label: `UTC-3 (${t('timezone.sao_paulo')})`,
                  },
                  {
                    value: 'UTC-5',
                    label: `UTC-5 (${t('timezone.new_york')})`,
                  },
                  { value: 'UTC-7', label: `UTC-7 (${t('timezone.denver')})` },
                  {
                    value: 'UTC-8',
                    label: `UTC-8 (${t('timezone.los_angeles')})`,
                  },
                  {
                    value: 'UTC-10',
                    label: `UTC-10 (${t('timezone.honolulu')})`,
                  },
                ]}
              />
            </Form.Item>
          </div>

          <div className="flex flex-col gap-2">
            <h2 className="text-lg font-semibold">{t('map_settings')}</h2>
            <Form.Item
              name="mapType"
              rules={[
                { required: true, message: t('map_type') + t('required') },
              ]}
            >
              <Radio.Group className="flex gap-4" buttonStyle="solid">
                <Radio.Button
                  value="google"
                  className="flex-1 text-center py-2"
                >
                  {t('google_map')}
                </Radio.Button>
                <Radio.Button value="gaode" className="flex-1 text-center py-2">
                  {t('gaode_map')}
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </div>

          <div className="flex flex-col gap-2">
            <h2 className="text-lg font-semibold">{t('location_info')}</h2>
            <Form.Item
              name="location"
              label={t('search_location')}
              rules={[
                {
                  required: true,
                  message: t('search_location') + t('required'),
                },
              ]}
            >
              <Input />
            </Form.Item>
            <div className="flex gap-2">
              <Form.Item
                name="longitude"
                label={t('longitude')}
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message: t('longitude') + t('required'),
                  },
                ]}
              >
                <Input readOnly />
              </Form.Item>
              <Form.Item
                name="latitude"
                label={t('latitude')}
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message: t('latitude') + t('required'),
                  },
                ]}
              >
                <Input readOnly />
              </Form.Item>
            </div>
            <div className="w-full h-[300px] bg-gray-100 rounded-lg overflow-hidden">
              {/* 选择的地图类型将在这里渲染 */}
              <GoogleMapProject/>
            </div>
          </div>
          <Form.Item>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4"
            >
              {t('save')}
            </button>
          </Form.Item>
        </Form>

        <div className="flex flex-col gap-2">
          <h2 className="text-lg font-semibold">{t('team_members')}</h2>
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">{t('admin')}</h3>
              <div className="flex flex-wrap gap-2">{/* 管理员头像列表 */}</div>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">{t('manager')}</h3>
              <div className="flex flex-wrap gap-2">{/* 经理头像列表 */}</div>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">{t('member')}</h3>
              <div className="flex flex-wrap gap-2">{/* 成员头像列表 */}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}