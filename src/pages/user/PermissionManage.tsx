import { useState } from 'react';
import { Button, Input, Space, Tag, Tooltip, Popconfirm, Modal, Form, Select, Tree, Divider, Popover, Checkbox } from 'antd';
import type { ColumnsType, ColumnType } from 'antd/es/table';
import type { TreeProps } from 'antd/es/tree';
import { PlusOutlined, DeleteOutlined, EditOutlined, RedoOutlined, AppstoreAddOutlined } from '@ant-design/icons';
import TableSelected from '@/components/TableSelected';
import classNames from 'classnames';

interface PermissionData {
  key: string;
  id: string;
  name: string;
  identifier: string;
  type: '菜单' | '页面' | '按钮';
  requestPath?: string;
  status: 'active' | 'inactive';
  description: string;
  createTime: string;
  parentId?: string;
  children?: any[];
  level?: number; // 当前节点的层级，从0开始
  isLeaf?: boolean; // 是否为叶子节点
  parentIdentifier?: string; // 父节点的标识符，用于构建权限标识符
}

export function PermissionManage() {
  const [searchText, setSearchText] = useState('');
  const [searchStatus, setSearchStatus] = useState<string|null>(null);
  const [isRotating, setIsRotating] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<PermissionData | null>(null);
  const [editForm] = Form.useForm();

  const columns: ColumnsType<PermissionData> = [
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      ellipsis: true,
    },
    {
      title: '权限标识',
      dataIndex: 'identifier',
      key: 'identifier',
      width: 200,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Tag color={type === '菜单' ? 'blue' : type === '页面' ? 'purple' : 'green'}>
          {type}
        </Tag>
      ),
    },
    {
      title: '请求地址',
      dataIndex: 'requestPath',
      key: 'requestPath',
      width: 200,
      ellipsis: true,
    },
    {
      title: '权限状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'active' ? 'success' : 'error'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此权限吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 模拟数据
  const data: PermissionData[] = [
    {
      key: '1',
      id: '1',
      name: '系统管理',
      identifier: 'system',
      type: '菜单',
      status: 'active',
      description: '系统管理相关权限',
      createTime: '2024-03-20',
      level: 0,
      isLeaf: false,
      children: [
        {
          key: '1-1',
          id: '1-1',
          name: '用户管理',
          identifier: 'system:user',
          type: '菜单',
          status: 'active',
          description: '用户管理相关权限',
          createTime: '2024-03-20',
          parentId: '1',
          level: 1,
          isLeaf: false,
          parentIdentifier: 'system',
          children: [
            {
              key: '1-1-1',
              id: '1-1-1',
              name: '用户查询',
              identifier: 'system:user:query',
              type: '页面',
              requestPath: '/api/system/user/list',
              status: 'active',
              description: '用户查询权限',
              createTime: '2024-03-20',
              parentId: '1-1',
              level: 2,
              isLeaf: true,
              parentIdentifier: 'system:user',
            },
            {
              key: '1-1-2',
              id: '1-1-2',
              name: '用户新增',
              identifier: 'system:user:add',
              type: '按钮',
              requestPath: '/api/system/user/add',
              status: 'active',
              description: '用户新增权限',
              createTime: '2024-03-20',
              parentId: '1-1',
              level: 2,
              isLeaf: true,
              parentIdentifier: 'system:user',
            },
            {
              key: '1-1-3',
              id: '1-1-3',
              name: '用户删除',
              identifier: 'system:user:delete',
              type: '按钮',
              requestPath: '/api/system/user/delete',
              status: 'active',
              description: '用户删除权限',
              createTime: '2024-03-20',
              parentId: '1-1',
              level: 2,
              isLeaf: true,
              parentIdentifier: 'system:user',
            },
          ],
        },
        {
          key: '1-2',
          id: '1-2',
          name: '角色管理',
          identifier: 'system:role',
          status: 'active',
          description: '角色管理相关权限',
          createTime: '2024-03-20',
          parentId: '1',
          children: [
            {
              key: '1-2-1',
              id: '1-2-1',
              name: '角色查询',
              identifier: 'system:role:query',
              status: 'active',
              description: '角色查询权限',
              createTime: '2024-03-20',
              parentId: '1-2',
            },
            {
              key: '1-2-2',
              id: '1-2-2',
              name: '角色新增',
              identifier: 'system:role:add',
              status: 'active',
              description: '角色新增权限',
              createTime: '2024-03-20',
              parentId: '1-2',
            },
            {
              key: '1-2-3',
              id: '1-2-3',
              name: '角色删除',
              identifier: 'system:role:delete',
              status: 'active',
              description: '角色删除权限',
              createTime: '2024-03-20',
              parentId: '1-2',
            },
          ],
        },
        {
          key: '1-3',
          id: '1-3',
          name: '权限管理',
          identifier: 'system:permission',
          status: 'active',
          description: '权限管理相关权限',
          createTime: '2024-03-20',
          parentId: '1',
          children: [
            {
              key: '1-3-1',
              id: '1-3-1',
              name: '权限查询',
              identifier: 'system:permission:query',
              status: 'active',
              description: '权限查询权限',
              createTime: '2024-03-20',
              parentId: '1-3',
            },
            {
              key: '1-3-2',
              id: '1-3-2',
              name: '权限新增',
              identifier: 'system:permission:add',
              status: 'active',
              description: '权限新增权限',
              createTime: '2024-03-20',
              parentId: '1-3',
            },
            {
              key: '1-3-3',
              id: '1-3-3',
              name: '权限删除',
              identifier: 'system:permission:delete',
              status: 'active',
              description: '权限删除权限',
              createTime: '2024-03-20',
              parentId: '1-3',
            },
          ],
        },
        {
          key: '1-4',
          id: '1-4',
          name: '系统监控',
          identifier: 'system:monitor',
          type: '菜单',
          status: 'active',
          description: '系统监控相关权限',
          createTime: '2024-03-20',
          parentId: '1',
          children: [
            {
              key: '1-4-1',
              id: '1-4-1',
              name: '服务监控',
              identifier: 'system:monitor:server',
              type: '菜单',
              status: 'active',
              description: '服务器性能监控',
              createTime: '2024-03-20',
              parentId: '1-4',
            },
            {
              key: '1-4-2',
              id: '1-4-2',
              name: '缓存监控',
              identifier: 'system:monitor:cache',
              type: '菜单',
              status: 'active',
              description: '缓存使用监控',
              createTime: '2024-03-20',
              parentId: '1-4',
            },
            {
              key: '1-4-3',
              id: '1-4-3',
              name: '在线用户',
              identifier: 'system:monitor:online',
              type: '菜单',
              status: 'active',
              description: '在线用户监控',
              createTime: '2024-03-20',
              parentId: '1-4',
            }
          ]
        },
        {
          key: '1-5',
          id: '1-5',
          name: '日志管理',
          identifier: 'system:log',
          type: '菜单',
          status: 'active',
          description: '系统日志管理',
          createTime: '2024-03-20',
          parentId: '1',
          children: [
            {
              key: '1-5-1',
              id: '1-5-1',
              name: '操作日志',
              identifier: 'system:log:operation',
              type: '菜单',
              status: 'active',
              description: '操作日志查看',
              createTime: '2024-03-20',
              parentId: '1-5',
            },
            {
              key: '1-5-2',
              id: '1-5-2',
              name: '登录日志',
              identifier: 'system:log:login',
              type: '菜单',
              status: 'active',
              description: '登录日志查看',
              createTime: '2024-03-20',
              parentId: '1-5',
            },
            {
              key: '1-5-3',
              id: '1-5-3',
              name: '系统日志',
              identifier: 'system:log:system',
              type: '菜单',
              status: 'active',
              description: '系统运行日志',
              createTime: '2024-03-20',
              parentId: '1-5',
            }
          ]
        }
      ],
    },
    {
      key: '2',
      id: '2',
      name: '设备管理',
      identifier: 'device',
      type: '菜单',
      status: 'active',
      description: '设备管理相关权限',
      createTime: '2024-03-20',
      children: [
        {
          key: '2-1',
          id: '2-1',
          name: '设备监控',
          identifier: 'device:monitor',
          status: 'active',
          description: '设备监控相关权限',
          createTime: '2024-03-20',
          parentId: '2',
          children: [
            {
              key: '2-1-1',
              id: '2-1-1',
              name: '实时监控',
              identifier: 'device:monitor:realtime',
              status: 'active',
              description: '设备实时监控权限',
              createTime: '2024-03-20',
              parentId: '2-1',
            },
            {
              key: '2-1-2',
              id: '2-1-2',
              name: '历史记录',
              identifier: 'device:monitor:history',
              status: 'active',
              description: '设备监控历史记录权限',
              createTime: '2024-03-20',
              parentId: '2-1',
            },
          ],
        },
        {
          key: '2-2',
          id: '2-2',
          name: '设备维护',
          identifier: 'device:maintenance',
          status: 'active',
          description: '设备维护相关权限',
          createTime: '2024-03-20',
          parentId: '2',
          children: [
            {
              key: '2-2-1',
              id: '2-2-1',
              name: '维护计划',
              identifier: 'device:maintenance:plan',
              status: 'active',
              description: '设备维护计划权限',
              createTime: '2024-03-20',
              parentId: '2-2',
            },
            {
              key: '2-2-2',
              id: '2-2-2',
              name: '维护记录',
              identifier: 'device:maintenance:record',
              status: 'active',
              description: '设备维护记录权限',
              createTime: '2024-03-20',
              parentId: '2-2',
            },
          ],
        },
        {
          key: '2-3',
          id: '2-3',
          name: '设备告警',
          identifier: 'device:alarm',
          type: '菜单',
          status: 'active',
          description: '设备告警管理',
          createTime: '2024-03-20',
          parentId: '2',
          children: [
            {
              key: '2-3-1',
              id: '2-3-1',
              name: '告警规则',
              identifier: 'device:alarm:rule',
              type: '菜单',
              status: 'active',
              description: '告警规则配置',
              createTime: '2024-03-20',
              parentId: '2-3',
            },
            {
              key: '2-3-2',
              id: '2-3-2',
              name: '告警记录',
              identifier: 'device:alarm:record',
              type: '菜单',
              status: 'active',
              description: '告警历史记录',
              createTime: '2024-03-20',
              parentId: '2-3',
            },
            {
              key: '2-3-3',
              id: '2-3-3',
              name: '告警统计',
              identifier: 'device:alarm:statistics',
              type: '菜单',
              status: 'active',
              description: '告警数据统计',
              createTime: '2024-03-20',
              parentId: '2-3',
            }
          ]
        },
        {
          key: '2-4',
          id: '2-4',
          name: '设备档案',
          identifier: 'device:profile',
          type: '菜单',
          status: 'active',
          description: '设备档案管理',
          createTime: '2024-03-20',
          parentId: '2',
          children: [
            {
              key: '2-4-1',
              id: '2-4-1',
              name: '基本信息',
              identifier: 'device:profile:info',
              type: '菜单',
              status: 'active',
              description: '设备基本信息',
              createTime: '2024-03-20',
              parentId: '2-4',
            },
            {
              key: '2-4-2',
              id: '2-4-2',
              name: '运行记录',
              identifier: 'device:profile:operation',
              type: '菜单',
              status: 'active',
              description: '设备运行记录',
              createTime: '2024-03-20',
              parentId: '2-4',
            },
            {
              key: '2-4-3',
              id: '2-4-3',
              name: '维修记录',
              identifier: 'device:profile:maintenance',
              type: '菜单',
              status: 'active',
              description: '设备维修记录',
              createTime: '2024-03-20',
              parentId: '2-4',
            }
          ]
        }
      ],
    },
  ];

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [addForm] = Form.useForm();

  const handleAdd = () => {
    setIsAddModalOpen(true);
    addForm.resetFields();
  };

  const handleAddModalOk = () => {
    addForm.validateFields().then((values) => {
      // 处理添加权限逻辑
      console.log('添加权限:', values);
      setIsAddModalOpen(false);
      addForm.resetFields();
    });
  };

  const handleAddModalCancel = () => {
    setIsAddModalOpen(false);
    addForm.resetFields();
  };

  const handleEdit = (record: PermissionData) => {
    setSelectedPermission(record);
    editForm.setFieldsValue(record);
    setIsEditModalOpen(true);
  };

  const handleEditModalOk = () => {
    editForm.validateFields().then((values) => {
      console.log('编辑权限:', values);
      setIsEditModalOpen(false);
      editForm.resetFields();
    });
  };

  const handleEditModalCancel = () => {
    setIsEditModalOpen(false);
    editForm.resetFields();
  };

  const handleDelete = (record: PermissionData) => {
    // 处理删除权限逻辑
    console.log('删除权限', record);
  };

  const handleBatchDelete = () => {
    // 处理批量删除权限逻辑
    console.log('批量删除权限');
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    // 处理搜索逻辑
    console.log('搜索:', value);
  };

  const handleStatusChange = (value: string) => {
    setSearchStatus(value);
    // 处理状态筛选逻辑
    console.log('状态筛选:', value);
  };

  const defaultCheckedList = columns.map((item) => item.key);
  const [checkedList, setCheckedList] = useState(defaultCheckedList);
  const CheckboxOptions = columns.map(({ key, title }) => ({
    label: title as React.ReactNode,
    value: key,
  }));

  const TableListShow = (
    <div className="flex flex-col justify-center items-center cursor-default">
      <Checkbox.Group
        className="flex flex-col gap-1"
        value={checkedList}
        options={CheckboxOptions}
        onChange={(value) => {
          setCheckedList(value as string[]);
        }}
      />
    </div>
  );

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <div className="flex">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            className="mr-2"
          >
            新增权限
          </Button>
          <Button
            type="dashed"
            icon={<DeleteOutlined />}
            onClick={handleBatchDelete}
            danger
          >
            批量删除
          </Button>
        </div>
        <div className="flex gap-2">
          <Input.Search
            placeholder="请输入搜索内容"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={handleSearch}
          />
          <Select
            placeholder="权限状态"
            allowClear
            style={{ width: 120 }}
            value={searchStatus}
            onChange={handleStatusChange}
            options={[
              { value: 'active', label: '启用' },
              { value: 'inactive', label: '停用' },
            ]}
          />
          <div className="card-base rounded-md bg-gray-200 dark:bg-gray-800 flex items-center gap-1">
            <button
              type="button"
              className={classNames(
                'w-8 h-[30px] flex items-center justify-center',
                { 'animate-spin': isRotating },
              )}
              onClick={() => {
                setIsRotating(true);
                setTimeout(() => setIsRotating(false), 1000);
              }}
            >
              <RedoOutlined className="!text-gray-900 dark:!text-gray-300" />
            </button>
            <Divider type="vertical" className="!mx-[0]" />
            <Popover
              placement="bottomRight"
              content={TableListShow}
              arrow={false}
              forceRender={true}
            >
              <div className="w-8 h-[30px] flex items-center justify-center">
                <AppstoreAddOutlined className="!text-gray-800 dark:!text-gray-300" />
              </div>
            </Popover>
          </div>
        </div>
      </div>
      <div className="h-[calc(100%-50px)]">
        <TableSelected<PermissionData>
          className="!border-none"
          columns={columns as ColumnType<PermissionData>[]}
          dataSource={data}
          total={2}
          callbackOnRows={(rows) => {
            console.log(rows);
          }}
          callbackPages={() => {
            // 处理分页变化
          }}
          tableProps={{
            expandable: {
              defaultExpandAllRows: true,
            },
            scroll: { x: 1030 },
          }}
        />
      </div>

      <Modal
        title="编辑权限信息"
        open={isEditModalOpen}
        onOk={handleEditModalOk}
        onCancel={handleEditModalCancel}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          initialValues={selectedPermission || {}}
        >
          <Form.Item
            name="parentId"
            label="上级权限"
          >
            <Select
              allowClear
              placeholder="请选择上级权限（不选则为最上级）"
              options={data.map(item => ({
                value: item.id,
                label: item.name,
                disabled: selectedPermission?.id === item.id
              })).concat(
                data.flatMap(item =>
                  item.children?.map(child => ({
                    value: child.id,
                    label: `${item.name} / ${child.name}`,
                    disabled: selectedPermission?.id === child.id
                  })) || []
                )
              )}
            />
          </Form.Item>
          <Form.Item
            name="name"
            label="权限名称"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="identifier"
            label="权限标识"
            rules={[{ required: true, message: '请输入权限标识' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择权限类型' }]}
          >
            <Select
              options={[
                { value: 'menu', label: '菜单' },
                { value: 'button', label: '按钮' },
                { value: 'api', label: 'API' },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="requestPath"
            label="请求地址"
            rules={[{ required: false }]}
          >
            <Input placeholder="请输入API请求地址" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入权限描述' }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择权限状态' }]}
          >
            <Select
              options={[
                { value: 'active', label: '启用' },
                { value: 'inactive', label: '停用' },
              ]}
            />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="新增权限信息"
        open={isAddModalOpen}
        onOk={handleAddModalOk}
        onCancel={handleAddModalCancel}
        width={600}
      >
        <Form
          form={addForm}
          layout="vertical"
        >
          <Form.Item
            name="parentId"
            label="上级权限"
          >
            <Select
              allowClear
              placeholder="请选择上级权限（不选则为最上级）"
              options={data.map(item => ({
                value: item.id,
                label: item.name
              })).concat(
                data.flatMap(item =>
                  item.children?.map(child => ({
                    value: child.id,
                    label: `${item.name} / ${child.name}`
                  })) || []
                )
              )}
            />
          </Form.Item>
          <Form.Item
            name="name"
            label="权限名称"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="identifier"
            label="权限标识"
            rules={[{ required: true, message: '请输入权限标识' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择权限类型' }]}
          >
            <Select
              options={[
                { value: 'menu', label: '菜单' },
                { value: 'button', label: '按钮' },
                { value: 'api', label: 'API' },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="requestPath"
            label="请求地址"
            rules={[{ required: false }]}
          >
            <Input placeholder="请输入API请求地址" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入权限描述' }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择权限状态' }]}
          >
            <Select
              options={[
                { value: 'active', label: '启用' },
                { value: 'inactive', label: '停用' },
              ]}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}