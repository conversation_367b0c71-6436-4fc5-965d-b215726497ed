import { LeftOutlined, ProductOutlined, RightOutlined, UserOutlined } from "@ant-design/icons";
import { useState } from "react";
import { createStyles } from 'antd-style';
import { Outlet, useNavigate } from "react-router-dom";
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import { ShieldCheck, UserLock } from "lucide-react";

type MenuItem = Required<MenuProps>['items'][number];
const useStyle = createStyles(({ css }) => {
  return {
    hideScrollbar: css`
      /* 隐藏滚动条但保持可滚动 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none;  /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
    `,
  };
});


export default function UserPage() {
    const [isShowTree, setIsShowTree] = useState<boolean>(true);
    const styles = useStyle();
    const navigate = useNavigate();


    const items: MenuItem[] = [
      {
        key: 'project_manage',
        label: '项目管理',
        icon: <ProductOutlined />,
      },
      {
        key: 'user_manage',
        label: '用户管理',
        icon: <UserOutlined />,
      },
      {
        key: 'role_manage',
        label: '角色管理',
        icon: <UserLock size="16" />,
      },
      {
        key: 'permissions_manage',
        label: '权限管理',
        icon: <ShieldCheck size="16" />,
      },
    ];
    const onClick: MenuProps['onClick'] = (e) => {
      console.log('click ', e);
      if (e.key === 'user_manage') {
        navigate('/manage/user');
        return;
      }
      if (e.key === 'role_manage') {
        navigate('/manage/role');
        return;
      }
      if (e.key === 'permissions_manage') {
        navigate('/manage/permission');
        return;
      }
      if (e.key === 'project_manage') {
        navigate('/manage/project');
      }
    };
    return (
      <div className="flex w-full h-full">
        <div
          className={`h-full flex-none transition-all duration-300 ${
            isShowTree ? 'w-[246px]' : 'w-0'
          }`}
        >
          <div
            className={`w-[246px] flex h-full shadow-lg box-border p-2 rounded-lg bg-white dark:bg-gray-800 flex-col relative transition-transform duration-300 ${isShowTree ? 'translate-x-0' : '-translate-x-[95%]'}`}
          >
            {/* 按钮样式调整 */}
            <button
              type="button"
              onClick={() => setIsShowTree(!isShowTree)}
              className={`flex items-center justify-center absolute ${isShowTree ? 'right-[-6px]' : 'right-[3px]'} w-[12px] h-[48px] bottom-[50%] bg-white dark:bg-gray-800 border-gray-800 dark:border-gray-500 border-2 rounded-full z-10 cursor-pointer !transition-all !duration-300`}
            >
              {isShowTree ? (
                <LeftOutlined
                  style={{ fontSize: '8px' }}
                  className={'!text-gray-900 dark:!text-gray-300'}
                />
              ) : (
                <RightOutlined
                  style={{ fontSize: '8px' }}
                  className={'!text-gray-900 dark:!text-gray-100'}
                />
              )}
            </button>

            {/* 添加动画容器 */}
            <div
              className={`h-full ${isShowTree ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300 overflow-hidden`}
            >
              <div className={'w-full flex-1 overflow-auto'}>
                <Menu
                  onClick={onClick}
                  style={{ width: 'auto', borderInlineEnd: '0px' }}
                  defaultSelectedKeys={['project_manage']}
                  mode="inline"
                  items={items}
                />
              </div>
            </div>
          </div>
        </div>
        <div
          className={`flex-1 h-full overflow-auto bg-white dark:bg-gray-800 transition-all duration-300 ${isShowTree ? 'ml-2' : 'ml-4'} shadow p-2 rounded-lg ${styles.styles.hideScrollbar}`}
        >
          <Outlet />
        </div>
      </div>
    );
}