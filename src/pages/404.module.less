// Button.module.css
.button {
  font-family: inherit;
  font-size: 20px;
  background: skyblue;
  color: white;
  padding: 0.7em 1em;
  padding-left: 0.9em;
  display: flex;
  align-items: center;
  border: none;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
  &:hover {
    background: skyblue !important;
  }
}

.buttonSpan {
  display: block;
  margin-left: 0.3em;
  transition: all 0.3s ease-in-out;
}

.svgIcon {
  display: block;
  transform-origin: center center;
  transition: transform 0.3s ease-in-out;
}

.button:hover .svgWrapper {
  animation: fly-1 0.6s ease-in-out infinite alternate;
}

.button:hover .svgIcon {
  transform: translateX(2.2em) rotate(45deg) scale(1.1);
}

.button:hover .buttonSpan {
  
  transform: translateX(5em);
}

.button:active {
  
  transform: scale(0.95);
}

@keyframes fly-1 {
  from {
    transform: translateY(0.1em);
  }

  to {
    transform: translateY(-0.1em);
  }
}
