import TreeBase from "@/components/TreeBase";
import type {CarbonEmissionType, SystemCardType} from "@/pages/PagesType.ts";
import {useTranslation} from "react-i18next";
import SmartLampPostIcons from "@/assets/svgIcons/SmartLampPostIcons.tsx";
import {Al<PERSON>, Divider, Progress} from "antd";
import type {ProgressProps} from "antd";
import SystemGateway from "@/assets/svgIcons/SystemGateway.tsx";
import LightingIcons from "@/assets/svgIcons/LightingIcons.tsx";
import PowerWaste from "@/assets/svgIcons/PowerWaste.tsx";
import SolarEnergy from "@/assets/svgIcons/SolarEnergy.tsx";
import CarbonEmission from "@/assets/svgIcons/CarbonEmission.tsx";
import CustomChart from "components/CustomCharts.tsx";
import type { EChartsOption } from "echarts/types/dist/echarts";
import TableBase from "@/components/TableBase";
import type { ColumnType } from "antd/lib/table";
import GoogleMapBase from "@/components/map/GoogleMapBase";
import AMapBase from "@/components/map/AMapBase";

export default function SystemOverview() {
    const {t} = useTranslation()
    const TraditionColors: ProgressProps['strokeColor'] = {
        '0%': '#19cfdf',
        '100%': '#8cc8f3',
    };
    const SolarColors: ProgressProps['strokeColor'] = {
        '0%': '#10e955',
        '100%': '#8cc8f3',
    };
    const Co2Colors: ProgressProps['strokeColor'] = {
        '0%': '#00b7c8',
        '100%': '#10f624',
    };


    // charts options
    const solarChartOption: EChartsOption = {
        tooltip: {
            formatter: (params) => {
                const dotStyle0 = 'display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:#01bfec';
                const dotStyle1 = 'display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:#ff8080';
                return `<div style="${dotStyle0}"></div>${// biome-ignore lint/suspicious/noExplicitAny: <explanation>
(params as any[])[0].seriesName}:   ${(params as any[])[0].value} kW
                       <br/><div style="${dotStyle1}"></div>${// biome-ignore lint/suspicious/noExplicitAny: <explanation>
(params as any[])[1].seriesName}:   ${(params as any[])[1].value} kW`;
            },
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            show: false,
            data: ['充电量', '放电量'],
            top: 10
        },
        grid: {
            left: '0',
            right: '0',
            bottom: '0',
            top: '0',
            containLabel: false
        },
        xAxis: {
            show: false,
            type: 'category',
            boundaryGap: false,
            data: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
            axisPointer: {
                type: 'shadow'
            }
        },
        yAxis: {
            show: false,
            type: 'value',
            name: '电量',
            axisLabel: {
                formatter: '{value} kW'
            }
        },
        series: [
            {
                name: '充电量',
                type: 'line',
                smooth: true,  // 平滑曲线
                stack: 'Total',  // 堆叠
                symbol: 'none',
                // 渐变色
                areaStyle: {
                    opacity: 0.8,
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(128, 255, 165, 0.8)' },
                            { offset: 1, color: 'rgba(1, 191, 236, 0)' }
                        ]
                    }
                },
                emphasis: {
                    focus: 'series'
                },
                lineStyle: {
                    width: 2,
                    color: '#01bfec'
                },
                data: [32, 45, 55, 43, 22, 33, 42, 32]
            },
            {
                name: '放电量',
                type: 'line',
                smooth: true,  // 平滑曲线
                stack: 'Total',  // 堆叠
                symbol: 'none',
                areaStyle: {
                    opacity: 0.8,
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(255, 128, 128, 0.8)' },
                            { offset: 1, color: 'rgba(255, 128, 128, 0)' }
                        ]
                    }
                },
                emphasis: {
                    focus: 'series'
                },
                lineStyle: {
                    width: 2,
                    color: '#ff8080'
                },
                data: [22, 32, 13, 34, 45, 23, 12, 21]
            }
        ]
    };


    interface DataType {
        key: string;
        name: string;
        age: number;
        address: string;
      }
      const originData = Array.from({ length: 50 }).map<DataType>((_, i) => ({
        key: i.toString(),
        name: `Edward ${i}`,
        age: 32,
        address: `London Park no. ${i}`,
      }));
      const columns= [
        {
          title: 'name',
          dataIndex: 'name',
          width: '25%',
          editable: true,
          align: 'center',
        },
        {
          title: 'age',
          dataIndex: 'age',
          width: '15%',
          editable: true,
          align: 'center',
        },
        {
          title: 'address',
          dataIndex: 'address',
          width: '40%',
          editable: true,
          align: 'center',
        }];
  return (
    <TreeBase>
        <div className="w-full h-full flex flex-col">
        <div className={'flex relative mb-2 h-fit items-stretch min-h-[466px]'}>
            <div className={'h-fit'} style={{width: "calc(80% - 0.5rem)"}}>
                <div className={'grid grid-cols-3 gap-2 w-full flex-wrap h-fit mb-2 items-start'}>
                    <ViewDataCard title={t("smart_lamp_post")} total={'2,800'} onLine={2000} offline={700} fault={100} icon={<SmartLampPostIcons/>}/>
                    <ViewDataCard title={t("smart_gateway")} total={'2,800'} onLine={2000} offline={700} fault={100} icon={<SystemGateway/>}/>
                    <ViewDataCard title={t("lighting")} total={'2,800'} onLine={2000} offline={700} fault={100} icon={<LightingIcons/>}/>
                    
                </div>
                <div className={'flex w-full h-fit justify-between items-start'}>
                    <CarbonEmissionCard title={t("system_view_power_card.power_waste")} icon={<PowerWaste />}>
                        <>
                                <div className={'h-[60px]'}>
                                    <div className={'text-gray-400 text-md'}>{t("system_view_power_card.actual_power_consumption")}</div>
                                    <div className={'mb-2 whitespace-nowrap'}>
                                        <span className={'font-bold text-2xl'}>10800000.00 </span>
                                        <span>kWh</span>
                                    </div>
                                </div>
                                <div className={'h-[60px]'}>
                                    <div className={'text-gray-400 text-md'}>{t("system_view_power_card.theoretical_power_consumption")}</div>
                                    <div className={'mb-2 whitespace-nowrap'}>
                                        <span className={'font-bold text-2xl'}>10800000.00 </span>
                                        <span>kWh</span>
                                    </div>
                                </div>
                                <div className={'h-[60px]'}>
                                    <div className={'text-gray-400 text-md'}>{t("system_view_power_card.save_power_consumption")}</div>
                                    <div className={"mb-2 whitespace-nowrap"}>
                                        <span className={'font-bold text-2xl'}>10800000.00 </span>
                                        <span>kWh</span>
                                    </div>
                                </div>
                                <div className={'text-gray-400 text-md flex justify-between'}>
                                    <div>{t("system_view_power_card.energy_saving_ratio")}</div>
                                    <div className={'text-blue-400'}>67%</div>
                                </div>
                                <Progress percent={67} strokeColor={TraditionColors} showInfo={false} status="active"/>
                        </>
                    </CarbonEmissionCard>
                    <CarbonEmissionCard title={t("system_view_power_card.solar_energy")} icon={<SolarEnergy />} >
                        <>
                            <div className={'h-[60px]'}>
                                <div className={'text-gray-400 text-md'}>{t("system_view_power_card.solar_power_consumption")}</div>
                                <div className={'mb-2 whitespace-nowrap'}>
                                    <span className={'font-bold text-2xl'}>10800000.00 </span>
                                    <span>kWh</span>
                                </div>
                            </div>
                            <div className={'h-[60px]'}>
                                <div className={'text-gray-400 text-md'}>{t("system_view_power_card.theoretical_power_consumption")}</div>
                                <div className={'mb-2 whitespace-nowrap'}>
                                    <span className={'font-bold text-2xl'}>10800000.00 </span>
                                    <span>kWh</span>
                                </div>
                            </div>
                            <div className={'h-[60px]'}>
                                <div className={'text-gray-400 text-md'}>{t("system_view_power_card.save_power_consumption")}</div>
                                <div className={"mb-2 whitespace-nowrap"}>
                                    <span className={'font-bold text-2xl'}>10800000.00 </span>
                                    <span>kWh</span>
                                </div>
                            </div>
                            <div className={'text-gray-400 text-md flex justify-between'}>
                                <div>{t("system_view_power_card.energy_saving_ratio")}</div>
                                <div className={'text-green-400'}>100%</div>
                            </div>
                            <Progress percent={100} strokeColor={SolarColors} showInfo={false} status="active"/>
                        </>
                    </CarbonEmissionCard>
                    <CarbonEmissionCard title={t("system_view_power_card.carbon_emission")} icon={<CarbonEmission />} >
                        <>
                            <div className={'h-[60px]'}>
                                <div className={'text-gray-400 text-md'}>{t("system_view_power_card.comprehensive_energy_saving")}</div>
                                <div className={'mb-2 whitespace-nowrap'}>
                                    <span className={'font-bold text-2xl'}>10800000.00 </span>
                                    <span>kWh</span>
                                </div>
                            </div>
                            <div className={'h-[60px]'}>
                                <div className={'text-gray-400 text-md'}>{t("system_view_power_card.total_carbon_emissions")}</div>
                                <div className={"mb-2 whitespace-nowrap"}>
                                    <span className={'font-bold text-2xl'}>10800000.00 </span>
                                    <span>kg</span>
                                </div>
                            </div>
                            {/* biome-ignore lint/style/useSelfClosingElements: <explanation> */}
                            <div className={'h-[60px]'}></div>
                            <div className={'text-gray-400 text-md flex justify-between'}>
                                <div>{t("system_view_power_card.overall_comprehensive_energy_saving_ratio")}</div>
                                <div className={'text-sky-400'}>67.01%</div>
                            </div>
                            <Progress percent={67} strokeColor={Co2Colors} showInfo={false} status="active"/>
                        </>
                    </CarbonEmissionCard>
                </div>
            </div>
            <div style={{height: "calc(100% + 2px)"}} className={'w-1/5 border border-gray-300/50 dark:border-white/20 absolute right-0 top-0 overflow-auto box-border flex flex-col p-1 h-full backdrop-blur-sm bg-white/30 dark:bg-gray-700/30 rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-gray-300/80 dark:hover:shadow-white/10'}>
                <div className="flex flex-col gap-2 box-border overflow-y-auto overflow-x-hidden p-1">
                <Alert
                    message="Error"
                    description="This is an error message about copywriting."
                    type="error"
                    showIcon
                    className=" w-full"
                    />
                <Alert
                    message="Informational Notes"
                    description="Additional description and information about copywriting."
                    type="info"
                    showIcon
                    className=" w-full"
                />
                <Alert
                    message="Warning"
                    description="This is a warning notice about copywriting."
                    type="warning"
                    showIcon
                    className=" w-full"
                />
                    <Alert
                        message="Warning"
                        description="This is a warning notice about copywriting."
                        type="warning"
                        showIcon
                        className=" w-full"
                    />
                    <Alert
                        message="Warning"
                        description="This is a warning notice about copywriting."
                        type="warning"
                        showIcon
                        className=" w-full"
                    />
                    <Alert
                        message="Warning"
                        description="This is a warning notice about copywriting."
                        type="warning"
                        showIcon
                        className=" w-full"
                    />
                    <Alert
                        message="Warning"
                        description="This is a warning notice about copywriting."
                        type="warning"
                        showIcon
                        className=" w-full"
                    />
                </div>
            </div>
        </div>
        <div className="w-full p-2 h-[180px] backdrop-blur-sm bg-white/30 dark:bg-gray-700/30 rounded-lg border border-gray-300/50 dark:border-white/20 shadow-lg transition-all duration-300 mb-2">
            <div className="text-gray-900 dark:text-gray-100 ml-2">
                {t("charts_charge_discharge")}
            </div>
            <Divider className="!mt-1 !mb-1"/>
            <div className="h-[138px]">
                <CustomChart option={solarChartOption}/>
            </div>
        </div>

        <div className="flex-1 flex min-h-[400px]">
            <div className="w-1/2 mr-1 p-2 h-full flex-col flex backdrop-blur-sm bg-white/30 dark:bg-gray-700/30 rounded-lg border border-gray-300/50 dark:border-white/20 shadow-lg transition-all duration-300">
                <div className="text-gray-900 dark:text-gray-100 ml-2">
                    {t("equipment_distribution_map")}
                </div>
                <Divider className="!mt-1 !mb-1"/>
                <div className="flex-1">
                    {/* <AMapBase/> */}
                    <GoogleMapBase locations={[{lat: 22.54992, lng: 113.94924}]} />
                </div>
            </div>
            <div className="w-1/2 overflow-auto p-2 ml-1 flex-col flex h-full backdrop-blur-sm bg-white/30 dark:bg-gray-700/30 rounded-lg border border-gray-300/50 dark:border-white/20 shadow-lg transition-all duration-300">
                <div className="text-gray-900 dark:text-gray-100 ml-2">
                    {t("the_remaining_life_of_the_equipment")}
                </div>
                <Divider className="!mt-1 !mb-1"/>
                <div className="flex-1">
                    <TableBase<DataType>
                        columns={columns as ColumnType<DataType>[]}
                        dataSource={originData}
                        total={100}
                        callbackPages={() => {
                            // 处理分页变化
                          }}
                        tableProps={{
                            
                        }}
                    />
                </div>
            </div>
        </div>
        </div>

    </TreeBase>
  );
}



// 设备卡片
const ViewDataCard = ({title, icon, total, onLine, offline, fault}: SystemCardType)=>{
    const {t} = useTranslation()
    return (
        <div className={'flex items-center backdrop-blur-sm bg-white dark:bg-gray-700/30 px-9 flex-1 rounded-lg border border-gray-300/50 dark:border-white/20 shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-gray-300/80 dark:hover:shadow-white/10'}>
            <div className={'flex-1 text-zinc-950 dark:text-gray-200 pt-[20px]'}>
                <div className={'font-bold mb-[10px] font-sans'}>{title}</div>
                <div className={'text-4xl leading-[50px] font-bold'}>{total}</div>
                <div className={'flex items-center mb-[20px] justify-between w-full'}>
                    <div className={'text-green-500 w-[30%] truncate'}>{t("on_line")}: {onLine}</div>
                    <Divider type={'vertical'} className={'border-gray-400'}/>
                    <div className={'text-gray-500 w-[30%] truncate'}>{t("off_line")}: {offline}</div>
                    <Divider type={'vertical'} className={'border-gray-400'}/>
                    <div className={'text-red-500 w-[30%] truncate'}>{t("fault")}: {fault}</div>
                </div>
            </div>
            <div className={'w-[50px] h-full'}>
                {icon}
            </div>
        </div>
    )
}

// 碳排放量卡片
const CarbonEmissionCard = ({title, icon, children}: CarbonEmissionType)=>{
    return (
        <div className={'flex items-center flex-wrap backdrop-blur-sm bg-white dark:bg-gray-700/30 px-9 w-[33%] rounded-lg border border-gray-300/50 dark:border-white/20 shadow-lg transition-all duration-300 pb-[20px]'}>
            <div className={'flex-1 text-zinc-950 dark:text-gray-200 pt-[20px] font-sans'}>
                <div className={'flex text-2xl mb-[10px] items-center'}>
                    { icon }
                    <div className={'ml-2'}>{title}</div>
                </div>
                {children}
            </div>
        </div>
    )
}