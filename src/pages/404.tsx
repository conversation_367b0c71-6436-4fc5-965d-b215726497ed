import type React from "react";
import { useTranslation } from "react-i18next";
import styles from "./404.module.less";
import { But<PERSON> } from "antd";

const NotFoundPage: React.FC = () => {
  const { t } = useTranslation();
  return (
    <div style={{ textAlign: "center", padding: "300px 0" }} className={"flex h-full w-full bg-[url(@/assets/texture.png)] items-center flex-col"}>
      <h1>404 - {t("not_found")}</h1>
      <p className={"my-7"}>{t("404")}</p>

      <Button
        className={styles.button}
        onClick={() => {}}
      >
        <div className={styles.svgWrapper}>
          {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" className={styles.svgIcon} aria-label="Navigation arrow icon">
            {/* biome-ignore lint/style/useSelfClosingElements: <explanation> */}
            <path fill="none" d="M0 0h24v24H0z"></path>
            {/* biome-ignore lint/style/useSelfClosingElements: <explanation> */}
            <path
              fill="currentColor"
              d="M1.946 9.315c-.522-.174-.527-.455.01-.634l19.087-6.362c.529-.176.832.12.684.638l-5.454 19.086c-.15.529-.455.547-.679.045L12 14l6-8-8 6-8.054-2.685z"
            ></path>
          </svg>
        </div>
        <span className={styles.buttonSpan}>{t("back_home")}</span>
      </Button>
    </div>
  );
};
export default NotFoundPage;