import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import type { AuthData } from "@/components/type";
import { encryptData, decryptData } from "@/utils/crypto";
import type { TreeDataNode } from "antd";
import type React from "react";

export const themeAtom = atomWithStorage<"dark" | "light">("theme", "light", {
  // 显式定义存储引擎
  getItem: (key) => {
    if (typeof window === "undefined") return "light"; // SSR处理
    const value = localStorage.getItem(key);
    return value === "dark" ? "dark" : "light"; // 严格类型校验
  },
  setItem: (key, value) => {
    localStorage.setItem(key, value);
  },
  removeItem: (key) => {
    localStorage.removeItem(key);
  },
});

//权限树
export const userAuthAtom = atomWithStorage<AuthData | null>("userAuth", null, {
  getItem: (key: string) => {
    const item = localStorage.getItem(key);
    return item ? decryptData(item) : null;
  },
  setItem: (key: string, value: AuthData | null) => {
    localStorage.setItem(key, encryptData(value));
  },
  removeItem: (key: string) => {
    localStorage.removeItem(key);
  },
});

export const ProjectAtom = atom<TreeDataNode[]>([]);

interface SelectedProject {
  keys: React.Key[];
  info: { node: { key: React.Key } };
}

export const SelectedProjectAtom = atom<SelectedProject | null>(null);