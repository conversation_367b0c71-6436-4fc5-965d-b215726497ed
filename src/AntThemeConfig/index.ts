import type { ThemeConfig } from "antd";

// 定义 antdThemes 对象
const antdThemes: { light: ThemeConfig; dark: ThemeConfig } = {
    light: {
        token: {
            // 其他全局 token
        },
        components: {
            Button: {
                colorPrimary: 'var(--color-elite)',
                colorPrimaryHover: "#0097a7",
            },
            Table: {
                colorBgContainer: "#ffffff",          // gray-50
                colorBgContainerDisabled: "#f9fafb",  // gray-100
                colorFillAlter: "#f3f4f6",           // gray-100
            },
            Tree: {
                nodeSelectedBg: 'var(--color-elite-auxiliary)',
                nodeSelectedColor: 'var(--color-gray-900)',
            },
            Menu: {
                itemSelectedBg: 'var(--color-elite-auxiliary)',
                itemSelectedColor: 'var(--color-gray-900)',
            },
            Radio: {
                colorPrimary: 'var(--color-elite)',
                colorPrimaryHover: "#0097a7",
                buttonSolidCheckedColor: 'var(--color-gray-900)',
            },
            Form: {
                // itemMarginBottom: 12,
            },
            Slider:{
                railSize: 8,
                dotSize: 16,
                borderRadiusXS: 8,
                handleSize: 12,
                handleSizeHover: 14,
                railBg: "rgba(0,0,0,0.05)",
                trackHoverBg: "rgba(0,0,0)",
                trackBg: "rgba(0,0,0,0.8)",
                handleActiveColor: "rgba(0,0,0,0.8)",
                colorPrimaryBorderHover: "rgba(0,0,0,0.9)",
                handleActiveOutlineColor: "rgba(0,0,0,0.2)",
                handleColor: "rgba(0,0,0,0.8)",
                
            }
        },
    },
    dark: {
        token: {
            // 其他全局 token
            colorBgElevated: "var(--color-gray-900)",
        },
        components: {
            Table: {
                colorBgContainer: "#1f2937",         
                colorBgContainerDisabled: "#374151",  
                colorFillAlter: "#374151",           
                colorBorderSecondary: "#111827",
            },
            Pagination: {
                itemActiveBg: "var(--color-gray-700)",
                itemBg: "var(--color-gray-800)",
                itemInputBg: "var(--color-gray-800)",
                itemLinkBg: "var(--color-gray-800)",
                colorBgContainer: "var(--color-gray-800)",
            },
            Select:{

                colorBgElevated: "var(--color-gray-800)",
                colorBgContainer: "var(--color-gray-800)",
                optionSelectedBg: "var(--color-gray-700)",
            },
            Slider:{
                railSize: 8,
                dotSize: 16,
                borderRadiusXS: 8,
                handleSize: 12,
                handleSizeHover: 14,
            },
            Button: {
                defaultBg: "var(--color-gray-800)",
                defaultHoverBg: "var(--color-gray-900)",
                defaultHoverColor: "var(--color-gray-50)",
                colorPrimary: "var(--color-gray-800)",
                colorPrimaryHover: "var(--color-gray-950)",
                primaryColor: "var(--color-gray-300)",
                defaultColor: "var(--color-gray-300)",
            },
            Segmented: {
                itemHoverBg: "var(--color-gray-950)",
                itemSelectedBg: "var(--color-gray-950)",
                trackBg: "var(--color-gray-800)",
            },
            Input: {
                colorBgContainer: "var(--color-gray-800)",
            },
            Radio: {
                buttonBg: "var(--color-gray-800)",
            },
        },
    },
};

export default antdThemes;
