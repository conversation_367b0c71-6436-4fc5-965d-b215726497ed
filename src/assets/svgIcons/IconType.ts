// 定义颜色类型
export type Color =
  | `#${string}` // 十六进制，如 #ff0000
  | `rgb(${number}, ${number}, ${number})` // RGB，如 rgb(255,0,0)
  | `rgba(${number}, ${number}, ${number}, ${number})` // RGBA，如 rgba(255,0,0,0.5)
  | `hsl(${number}, ${number}%, ${number}%)` // HSL，如 hsl(0, 100%, 50%)
  | "currentColor" // 继承当前颜色
  | "inherit"; // 继承父元素颜色
export interface IconType {
  color?: Color;
  width?: number;
  height?: number;
  className?: string;
}
