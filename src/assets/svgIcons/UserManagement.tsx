import type { IconType } from "@/assets/svgIcons/IconType";

export default function UserManagement({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width={width} height={height}>
      <title>User Management</title>
      <path
        d="M415.5392 454.0928a193.6896 193.6896 0 1 0-193.4336-193.4336 194.1504 194.1504 0 0 0 193.4336 193.4336z m0-320.0512a127.1808 127.1808 0 0 1 126.5664 126.6176 126.3616 126.3616 0 1 1-126.5664-126.6176zM565.0432 889.9584H118.4256c0-92.928 0-139.6224 16.128-171.008A148.0704 148.0704 0 0 1 199.68 653.4144c31.8976-16.1792 80.7936-16.1792 177.7664-16.1792h187.648a33.6896 33.6896 0 0 0 0-67.328H377.3952c-107.52 0-162.048 0-208.2816 23.808a215.04 215.04 0 0 0-94.72 94.6688C51.2 734.6176 51.2 788.48 51.2 896v27.392a33.3824 33.3824 0 0 0 33.536 34.048h480.3072a33.6896 33.6896 0 0 0 0-67.328z"
        fill={color}
      >

      </path>
      <path
        d="M967.2192 724.7872l-75.4176-134.7072a45.312 45.312 0 0 0-39.5264-22.8864h-149.4528a44.5952 44.5952 0 0 0-39.0656 22.8864l-75.8272 134.7072a45.1072 45.1072 0 0 0 0 43.9808l75.8272 134.656a44.5952 44.5952 0 0 0 39.0656 22.8864h149.4528a45.312 45.312 0 0 0 39.5264-22.8864l75.4176-134.656a45.1072 45.1072 0 0 0 0-43.9808z m-128 134.1952h-122.88l-63.2832-112.2304 63.2832-112.2304h122.88l63.2832 112.2304z"
        fill={color}
      >

      </path>
      <path d="M777.7792 746.752m-56.1152 0a56.1152 56.1152 0 1 0 112.2304 0 56.1152 56.1152 0 1 0-112.2304 0Z" fill={color} >

      </path>
    </svg>
  );
}
