import type { IconType } from "@/assets/svgIcons/IconType";

export default function SystemIcons({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width={width} height={height}>
      <title>System</title>
      <path
        d="M862.265 687.371l-700.996-1.016V163.47l700.996-1.31v525.211z m-635.371-66.545l569.746 0.824V227.908l-569.746 1.064v391.854z m133.308-79.254l-46.403-46.404 150.23-150.228 84.312 84.313 115.457-115.457 46.404 46.404-161.861 161.862-84.313-84.313-103.826 103.823z"
        fill={color}
      >

      </path>
      <path d="M161.999 774.499H862v65.625H161.999z" fill={color} >

      </path>
    </svg>
  );
}
