import type { IconType } from "@/assets/svgIcons/IconType";

export default function NoticeIcons({ color = "#f4ea2a", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={width} height={height}>
      <title>Notice</title>
      <path d="M212.8 436.31h59.59a357.13 357.13 0 0 1 357.12 357.13H199.36V449.75a13.44 13.44 0 0 1 13.44-13.44z" fill="#FC8D8D" >

      </path>
      <path
        d="M392 150.21h240a32 32 0 0 0 0-64H392a32 32 0 1 0 0 64zM893 761.42h-36.36V535.87c0-190-154.6-344.65-344.65-344.65S167.36 345.83 167.36 535.87v225.55H131a32 32 0 0 0 0 64h203.25a196.9 196.9 0 0 0 355.66 0H893a32 32 0 0 0 0-64zM512.07 873.79a132.7 132.7 0 0 1-102.21-48.34H614.3a132.81 132.81 0 0 1-102.23 48.34zM231.38 761.42V535.87c0-154.74 125.9-280.62 280.63-280.62s280.61 125.88 280.61 280.62v225.55z"
        fill="#f4ea2a"
        data-spm-anchor-id="a313x.search_index.0.i26.3cd53a81CZn4RD"
        className="selected"
      >

      </path>
      <path
        d="M552.38 355.33a32 32 0 1 0 0 64 105.07 105.07 0 0 1 105 104.95 32 32 0 0 0 64 0c-0.02-93.15-75.82-168.95-169-168.95z"
        fill="#f4ea2a"
        data-spm-anchor-id="a313x.search_index.0.i27.3cd53a81CZn4RD"
        className="selected"
      >

      </path>
    </svg>
  );
}
