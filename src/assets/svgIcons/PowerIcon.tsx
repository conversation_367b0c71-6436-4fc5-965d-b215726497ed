import type { IconType } from "@/assets/svgIcons/IconType";

export default function PowerIcon({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={width} height={height}>
      <title>PowerIcon</title>
      <path 
      d="M479.744 380.928h107.008l-35.84 214.016 215.04-267.264h-107.008l17.92-214.528-197.12 267.776z" fill={color}/>
      <path d="M982.016 734.208h-192l-36.864-101.376c-4.608-12.8-15.872-22.016-29.184-23.552-1.536 0-3.584-0.512-5.12-0.512-11.776 0-23.04 5.632-29.696 14.848l-130.56 178.176-130.048-286.72c-5.12-11.776-16.384-19.456-28.672-21.504-1.536 0-3.072-0.512-4.608-0.512-11.264 0-21.504 5.12-28.672 13.824l-121.856 152.576H41.984c-20.48 0-36.864 16.384-36.864 36.864s16.384 36.864 36.864 36.864h220.672c11.264 0 21.504-5.12 28.672-13.824l95.232-119.296 131.072 289.28c5.12 11.776 16.896 19.968 29.696 21.504h3.584c11.776 0 22.528-5.632 29.696-14.848l126.976-173.568 22.016 60.928c5.12 14.336 18.944 24.064 34.304 24.064h217.6c9.728 0 18.944-3.584 26.112-10.752 7.168-7.168 10.752-16.384 10.752-26.112 0.512-19.968-15.872-36.352-36.352-36.352z" 
      fill={color}/>
    </svg>
  );
}
