import type { IconType } from "@/assets/svgIcons/IconType";

export default function FaultIcons({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width={width} height={height}>
      <title>FaultIcons</title>
      <path
        d="M805.290667 597.674667a42.666667 42.666667 0 0 1 12.828444 12.814222l163.754667 254.748444A56.888889 56.888889 0 0 1 934.016 952.888889H630.428444a56.888889 56.888889 0 0 1-47.857777-87.651556l163.754666-254.748444a42.666667 42.666667 0 0 1 58.965334-12.8zM782.222222 839.111111a28.444444 28.444444 0 1 0 0 56.888889 28.444444 28.444444 0 0 0 0-56.888889z m-14.222222-312.888889c9.486222 0 18.446222 2.318222 26.311111 6.442667a56.931556 56.931556 0 0 0-59.847111 24.704L533.731556 867.555556H142.222222a56.888889 56.888889 0 0 1-56.888889-56.888889V583.111111a56.888889 56.888889 0 0 1 56.888889-56.888889h625.777778z m14.222222 142.222222a28.444444 28.444444 0 0 0-28.444444 28.444445v85.333333a28.444444 28.444444 0 1 0 56.888889 0v-85.333333a28.444444 28.444444 0 0 0-28.444445-28.444445zM768 99.555556a56.888889 56.888889 0 0 1 56.888889 56.888888v227.555556a56.888889 56.888889 0 0 1-56.888889 56.888889H142.222222a56.888889 56.888889 0 0 1-56.888889-56.888889V156.444444a56.888889 56.888889 0 0 1 56.888889-56.888888h625.777778z m-440.888889 71.111111h-113.777778a28.444444 28.444444 0 1 0 0 56.888889h113.777778a28.444444 28.444444 0 1 0 0-56.888889z"
        fill={color}
      >

      </path>
    </svg>
  );
}
