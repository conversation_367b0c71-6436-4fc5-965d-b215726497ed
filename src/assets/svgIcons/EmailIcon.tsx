import type { IconType } from "@/assets/svgIcons/IconType";

export default function EmailIcon({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width={width} height={height}>
      <title>Email</title>
      <path
        d="M819.2 798.72H204.8a61.44 61.44 0 0 1-61.44-61.44V266.24a61.44 61.44 0 0 1 61.44-61.44h614.4a61.44 61.44 0 0 1 61.44 61.44v471.04a61.44 61.44 0 0 1-61.44 61.44zM262.41024 266.24A57.07776 57.07776 0 0 0 204.8 322.7648v357.9904A57.07776 57.07776 0 0 0 262.41024 737.28h499.2A57.07776 57.07776 0 0 0 819.2 680.7552V322.7648a55.05024 55.05024 0 0 0-3.31776-18.18624L512 532.48 245.76 327.68l20.48-20.48h40.96l204.8 163.84 259.42016-203.83744a58.49088 58.49088 0 0 0-9.80992-0.96256H262.41024z"
        fill={color}
      >

      </path>
    </svg>
  );
}
