import type { IconType } from "./IconType";
export default function StatusIcons({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
    return (
        <svg aria-hidden="true" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={width} height={height}>
            <title>More</title>
            <path d="M992.098 573.282H760.517l-89.56-314.407a31.862 31.862 0 0 0-30.878-22.567h-0.355a31.705 31.705 0 0 0-30.68 23.237L507.668 647.562l-93.224-267.816a31.902 31.902 0 0 0-29.026-20.873 30.247 30.247 0 0 0-30.72 18.51l-87.71 195.9H32.06c-17.644 0-31.98 13.705-31.98 30.601 0 16.936 14.336 30.642 31.98 30.642h256c12.84 0 24.419-7.287 29.42-18.59l62.858-140.327 101.376 291.13a31.902 31.902 0 0 0 30.366 20.953h1.22a31.665 31.665 0 0 0 29.815-23.237l98.304-376.359 63.803 223.862a31.823 31.823 0 0 0 30.877 22.568h256c17.645 0 31.98-13.706 31.98-30.642 0-16.935-14.335-30.602-31.98-30.602z" fill={color} >
            </path>
        </svg>
    )

}