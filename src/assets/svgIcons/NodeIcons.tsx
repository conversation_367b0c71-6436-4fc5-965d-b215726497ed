import type { IconType } from "./IconType";
export default function NodeIcons({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
    return (
        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={width} height={height}>
            <title>node</title>
            <path d="M341.312 128c0-47.168 38.208-85.312 85.376-85.312h170.624c47.168 0 85.376 38.144 85.376 85.312v170.688C682.688 345.728 644.48 384 597.312 384h-42.624v85.312h362.624a42.688 42.688 0 1 1 0 85.376h-106.624V640h42.624c47.168 0 85.376 38.208 85.376 85.312V896c0 47.104-38.208 85.312-85.376 85.312h-170.624A85.312 85.312 0 0 1 597.312 896v-170.688c0-47.104 38.208-85.312 85.376-85.312h42.624V554.688H298.688V640h42.624c47.168 0 85.376 38.208 85.376 85.312V896c0 47.104-38.208 85.312-85.376 85.312H170.688A85.312 85.312 0 0 1 85.312 896v-170.688c0-47.104 38.208-85.312 85.376-85.312h42.624V554.688H106.688a42.688 42.688 0 1 1 0-85.376h362.624V384h-42.624a85.312 85.312 0 0 1-85.376-85.312V128z m256 0H426.688v170.688h170.624V128zM170.688 896h170.624v-170.688H170.688V896z m682.624 0v-170.688h-170.624V896h170.624z" fill={color}>

            </path>
        </svg>
    )

}