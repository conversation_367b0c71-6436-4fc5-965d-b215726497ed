import type { IconType } from "./IconType";

export default function ReportIcon({ color = "#fff", width = 20, height = 20 }: Readonly<IconType>) {
  return (
    <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width={width} height={height}>
      <title>Report</title>
      <path
        d="M960 0a64 64 0 0 1 64 64v704a64 64 0 0 1-64 64H544v128H832v64H192v-64h288v-128H64a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64h896z m0 64H64v704h896V64zM320 224c22.464 0 44.032 3.84 64 10.944V352h117.12A192 192 0 1 1 320 224zM768 512v64h-128V512h128zM320 288a128 128 0 1 0 128 128H320v-128zM768 384v64h-128V384h128z m64-128v64h-192V256h192z"
        fill={color}
      >

      </path>
    </svg>
  );
}
