import type { IconType } from '@/assets/svgIcons/IconType';

export default function Signal4GBreakIcon({
                                         color = '#fff',
                                         width = 20,
                                         height = 20,
                                     }: Readonly<IconType>) {
    return (
        <svg
            className="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
        >
        <title>Signal4GIcon</title>
            <path
                d="M1143.484416 0h35.762875a78.01872 78.01872 0 0 1 77.984366 78.01872v867.96256a78.01872 78.01872 0 0 1-78.01872 78.01872h-35.728521a78.01872 78.01872 0 0 1-78.01872-78.01872v-867.96256a78.01872 78.01872 0 0 1 78.01872-78.01872z"
                fill={color} />
            <path
                d="M724.533163 341.344785m68.708693 0l54.348576 0q68.708693 0 68.708692 68.708692l0 545.23783q0 68.708693-68.708692 68.708693l-54.348576 0q-68.708693 0-68.708693-68.708693l0-545.23783q0-68.708693 68.708693-68.708692Z"
                fill={color} />
            <path
                d="M383.600631 585.123226m68.708692 0l54.348576 0q68.708693 0 68.708693 68.708693l0 301.425034q0 68.708693-68.708693 68.708693l-54.348576 0q-68.708693 0-68.708692-68.708693l0-301.425034q0-68.708693 68.708692-68.708693Z"
                fill={color} />
            <path
                d="M0 780.187204m68.708693 0l54.348575 0q68.708693 0 68.708693 68.708693l0 106.39541q0 68.708693-68.708693 68.708693l-54.348575 0q-68.708693 0-68.708693-68.708693l0-106.39541q0-68.708693 68.708693-68.708693Z"
                fill={color} />
            <path
                d="M151.846211 428.226927a34.354346 34.354346 0 0 1-24.288523-10.065824 34.354346 34.354346 0 0 1 0-48.577046l274.83477-274.83477a34.354346 34.354346 0 0 1 48.577046 0 34.354346 34.354346 0 0 1 0 48.577046l-274.834771 274.83477a34.354346 34.354346 0 0 1-24.288522 10.065824z"
                fill={color} />
            <path
                d="M426.680981 428.226927a34.354346 34.354346 0 0 1-24.288523-10.065824l-274.83477-274.83477a34.354346 34.354346 0 0 1 0-48.577046 34.354346 34.354346 0 0 1 48.577045 0l274.834771 274.83477a34.354346 34.354346 0 0 1-24.288523 58.64287z"
                fill={color} />
        </svg>
    );
}
