@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --color-elite: #00acc1;  /* 主题色 */
  --color-elite-auxiliary: #e0f7fa;  /* 辅助色 */
  --color-boxBg: #f3f4f6;
  /* 00acc1 */
}

/* 自定义边框样式 */
@layer components {
  .card-base {
    @apply border backdrop-blur-lg bg-white dark:bg-gray-700 border-gray-300/50 dark:border-white/20 shadow;
  }
}

/* Root 容器样式设置 */
    #root {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    box-sizing: border-box;
    margin: 0;
    min-width: 1600px;
    min-height: 860px;
    padding: 0;

    @media screen and (max-width: 1600px) {
      height: calc(100vh - 16px);
    }

    @media screen and (max-height: 850px) {
      width: calc(100vw - 16px);
    }
  }

/* 全局 body 样式设置 */
  body {
  margin: 0;
  padding: 0;
  overflow: auto;
  box-sizing: border-box;

  /* 滚动条样式 - Webkit 内核浏览器（Chrome、Safari、Edge等） */
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: transparent;
  }

  /* 滚动条滑块样式 */
  ::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-bg);
    border-radius: 4px;

    /* 滑块悬停效果 */
    &:hover {
      background: var(--scrollbar-thumb-hover-bg);
    }
  }

  /* 滚动条轨道样式 */
  ::-webkit-scrollbar-track {
    background: transparent;
  }
}

/* 全局 CSS 变量定义 */
:root {
  /* 使用 Tailwind 的颜色变量 */
  --scrollbar-thumb-bg: var(--color-gray-300);  /* 滑块默认色 */
  --scrollbar-thumb-hover-bg: var(--color-gray-300);  /* 滑块悬停色 */
  
  /* 添加全局主题过渡动画 */
  --theme-transition-duration: 0.3s;
  --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}


/* 深色模式下的颜色设置 - 使用 Tailwind dark 类选择器 */
:root.dark {
  --scrollbar-thumb-bg: var(--color-gray-500);
  --scrollbar-thumb-hover-bg: var(--color-gray-400);
}
