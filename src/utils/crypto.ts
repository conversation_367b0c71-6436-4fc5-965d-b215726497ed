import CryptoJS from "crypto-js";
import { AuthData } from "@/components/type";

// 加密配置（生产环境应通过安全方式存储）
const CRYPTO_CONFIG = {
  key: CryptoJS.enc.Utf8.parse("your-32bytes-secret-key"), // 32位密钥
  iv: CryptoJS.enc.Utf8.parse("your-16bytes-iv"), // 16位初始向量
};

// 加密方法
export const encryptData = (data: AuthData | null): string => {
  if (!data) return "";

  const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), CRYPTO_CONFIG.key, {
    iv: CRYPTO_CONFIG.iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};

// 解密方法（带错误处理）
export const decryptData = (cipherText: string): AuthData | null => {
  // 此处iv 、 key建议送服务器下发。
  try {
    if (!cipherText) return null;

    const decrypted = CryptoJS.AES.decrypt(cipherText, CRYPTO_CONFIG.key, {
      iv: CRYPTO_CONFIG.iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    const str = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));

    // if (str?.expiresAt && str.expiresAt < Date.now()) {
    //   console.log('令牌已过期，自动清除')
    //   localStorage.removeItem('auth')
    //   return null
    // }

    return str as AuthData;
  } catch (error) {
    console.error("解密失败，清除无效存储 => ", error);
    localStorage.removeItem("userAuth"); // 自动清理无效数据
    return null;
  }
};
