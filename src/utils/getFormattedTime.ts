// utils/getFormattedTimeIntl.ts
type DateFormat = "short" | "medium" | "long" | "full" | "custom";
type TimeFormat = "short" | "medium" | "long" | "full" | "custom";

export function getFormattedTimeIntl(dateFormat: DateFormat = "short", timeFormat: TimeFormat = "short", customOptions: Intl.DateTimeFormatOptions = {}): string {
  const now = new Date();

  const dateOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  };

  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: "2-digit",
    minute: "2-digit",
  };

  const optionsMap: Record<DateFormat | TimeFormat, Intl.DateTimeFormatOptions> = {
    short: {},
    medium: { dateStyle: "medium", timeStyle: "short" },
    long: { dateStyle: "long", timeStyle: "short" },
    full: { dateStyle: "full", timeStyle: "short" },
    custom: {},
  };

  const options = {
    ...dateOptions,
    ...timeOptions,
    ...optionsMap[dateFormat],
    ...optionsMap[timeFormat],
    ...customOptions,
  };

  return new Intl.DateTimeFormat(undefined, options).format(now);
}
