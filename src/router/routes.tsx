// src/router/routes.tsx
import { Navigate, type RouteObject } from 'react-router-dom';
import NotFound from "@/pages/404.tsx"
import App from '@/App.tsx'
import SystemView from '@/pages/systemOverview/index'
import Login from "@/pages/login/index.tsx"
import EquipmentView from '@/pages/equipment/index'
import Iot from 'pages/equipment/iot/index'
import Gateway from '@/pages/equipment/gateway/gateway';
import MapView from '@/pages/mapView/index';
import GatewayPage from '@/pages/equipment/gateway/gateway_new';
import DevicePage from '@/pages/equipment/iot/device';
import UserPage from '@/pages/user/index';
import { RoleManage } from '@/pages/user/RoleManage';
import UserManage from '@/pages/user/UserManage';
import { PermissionManage } from '@/pages/user/PermissionManage';
import ProjectManage from '@/pages/user/ProjectManage';
// modernization

export const routes: RouteObject[] = [
  {
    path: '/',
    element: <App />,
    children: [
      { index: true, element: <Navigate to="system/overview" /> },
      { path: 'system/overview', element: <SystemView /> },
      {
        path: 'equipment/overview',
        element: <EquipmentView />,
        children: [
          { index: true, element: <Navigate to="iot" /> },
          { path: 'iot', element: <Iot /> },
          { path: 'gateway', element: <Gateway /> },
          { path: 'gatewaynew', element: <GatewayPage /> },
          { path: 'iotnew', element: <DevicePage /> },
        ],
      },
      { path: 'map_preview', element: <MapView /> },
      { path: 'manage', element: <UserPage />,
        children: [
          { index: true, element: <Navigate to="project" /> },
          { path: 'user', element: <UserManage /> },
          { path: 'role', element: <RoleManage /> },
          { path: 'permission', element: <PermissionManage /> },
          { path: 'project', element: <ProjectManage /> },
        ],
      },
    ],
  },
  { path: '/login', element: <Login /> },
  { path: '*', element: <NotFound /> },
];