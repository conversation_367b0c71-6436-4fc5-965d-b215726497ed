import { StrictMode, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import {ConfigProvider, theme as themeAnt} from "antd";
import { I18nextProvider, useTranslation } from 'react-i18next'
import {AppRouter} from "./router";
import antdThemes from "./AntThemeConfig";
import '@ant-design/v5-patch-for-react-19';
import { useAtom } from 'jotai';
import { themeAtom } from './jotai/atoms';
import dayjs from 'dayjs';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import caES from 'antd/locale/ca_ES';
import roRO from 'antd/locale/ro_RO';

function App() {
  const [theme] = useAtom(themeAtom);
  const { i18n } = useTranslation();
  const [antdLocale, setAntdLocale] = useState(zhCN);

  useEffect(() => {
    document.documentElement.classList.toggle("dark", theme === "dark");
  }, [theme]);

  useEffect(() => {
    type Locale = typeof zhCN;
    const localeMap: Record<string, Locale> = {
      'en': enUS,
      'zh': zhCN,
      'ca': caES,
      'ro': roRO,
    };
    
    const dayjsLocaleMap: Record<string, string> = {
      'en': 'en',
      'zh': 'zh-cn',
      'ca': 'ca',
      'ro': 'ro',
    };

    const currentLang = i18n.language;
    setAntdLocale(localeMap[currentLang] || zhCN);
    dayjs.locale(dayjsLocaleMap[currentLang] || 'zh-cn');
  }, [i18n.language]);

  return (
    <StrictMode>
      <ConfigProvider
        locale={antdLocale}
        theme={{
          algorithm: theme === "light" ? themeAnt.defaultAlgorithm : themeAnt.darkAlgorithm,
          token: antdThemes[theme]?.token,
          components: antdThemes[theme]?.components,
        }}
      >
        <I18nextProvider i18n={i18n}>
          <AppRouter />
        </I18nextProvider>
      </ConfigProvider>
    </StrictMode>
  );
}

createRoot(document.getElementById('root') || document.createElement('div')).render(<App />);
