import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from "axios";
import qs from "qs";

let baseURL: string | undefined = undefined;
// 环境的切换
if (process.env.NODE_ENV === "development") {
  baseURL = "https://www.baidu.com";
} else if (process.env.NODE_ENV === "debug") {
  baseURL = "https://www.ceshi.com";
} else if (process.env.NODE_ENV === "production") {
  baseURL = "https://www.production.com";
}
// 创建一个 Axios 实例
const apiInstance: AxiosInstance = axios.create({
  baseURL: baseURL, // 设置基础URL
  timeout: 10000, // 设置请求超时
  headers: {
    "Content-Type": "x-www-form-urlencoded",
  },
});

// 请求拦截器
apiInstance.interceptors.request.use(
  (config) => {
    // const token = localStorage.getItem('token');
    // if (token && config.url!=="/login") {
    //     config.headers.Authorization = `Bearer ${token}`;
    // } else {
    // messageApi.open({
    //     type: 'error',
    //     content: t('not_login'),
    // });
    //     // token 不存在，跳转到登录页面
    //     history.push('/login');
    // }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 响应拦截器
apiInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做些什么
    // 例如，可以处理特定的响应码
    return response.data;
  },
  (error) => {
    if (error.response) {
      console.error("Error:", error.response.data.message || error.response.data);
    } else if (error.request) {
      console.error("No response received:", error.request);
    } else if (error.message === "canceled") {
      console.error("Request canceled:", error.message);
    } else {
      console.error("Error:", error.message);
    }
    console.error("Error:", error);
    return Promise.reject(error);
  },
);

// HTTP 请求封装
const createHttpMethod = <P = any, R = any>(method: "get" | "post" | "delete" | 'put') => {
  return (url: string, dataOrParams?: P, config?: AxiosRequestConfig) => {
    const controller = new AbortController();
    const signal = controller.signal;

    const res = async () => {
      try {
        let response: AxiosResponse<R>;
        switch (method) {
          case "get":
            response = await apiInstance.get<R>(url, {
              params: qs.stringify(dataOrParams),
              signal,
              ...config,
            });
            break;
          case "post":
            response = await apiInstance.post<R>(url, dataOrParams, {
              signal,
              ...config,
            });
            break;
          case "delete":
            response = await apiInstance.delete<R>(url, {
              params: qs.stringify(dataOrParams),
              signal,
              ...config,
            });
            break;
          case "put":
          response = await apiInstance.put<R>(url, {
            params: qs.stringify(dataOrParams),
            signal,
            ...config,
          });
          break;
          default:
            throw new Error(`Unsupported HTTP method: ${method}`);
        }

        return response;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log(`${method.toUpperCase()} request canceled:`, error.message);
        } else {
          console.error(`${method.toUpperCase()} request error:`, error);
          throw error;
        }
      }
    };

    return { res, cancel: () => controller.abort() };
  };
};

// 封装请求的对象
const __http = {
  get: createHttpMethod<Record<any,any>, any>("get"),
  post: createHttpMethod<Record<any,any>, any>("post"),
  del: createHttpMethod<Record<any,any>, any>("delete"),
  put: createHttpMethod<Record<any,any>, any>("put"),
};

export default __http;