Stack trace:
Frame         Function      Args
0007FFFF95B0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF84B0) msys-2.0.dll+0x1FE8E
0007FFFF95B0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9888) msys-2.0.dll+0x67F9
0007FFFF95B0  000210046832 (000210286019, 0007FFFF9468, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF95B0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF95B0  000210068E24 (0007FFFF95C0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9890  00021006A225 (0007FFFF95C0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEF8100000 ntdll.dll
7FFEF6DA0000 KERNEL32.DLL
7FFEF5A70000 KERNELBASE.dll
7FFEF66B0000 USER32.dll
7FFEF5560000 win32u.dll
7FFEF8050000 GDI32.dll
7FFEF52E0000 gdi32full.dll
7FFEF54B0000 msvcp_win.dll
7FFEF5710000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEF6900000 advapi32.dll
7FFEF6060000 msvcrt.dll
7FFEF6420000 sechost.dll
7FFEF6110000 RPCRT4.dll
7FFEF4900000 CRYPTBASE.DLL
7FFEF59D0000 bcryptPrimitives.dll
7FFEF7310000 IMM32.DLL
7FFEC8A60000 windhawk.dll
7FFEEEB10000 WINHTTP.dll
7FFEC8770000 explorer-details-better-file-sizes_1.4.8_772957.dll
7FFEF73C0000 ole32.dll
7FFEF6A10000 combase.dll
7FFEF6230000 OLEAUT32.dll
7FFEF7920000 SHELL32.dll
7FFEF5860000 wintypes.dll
7FFEF1B20000 PROPSYS.dll
7FFEC85D0000 libc++.dll
7FFEC8590000 libunwind.dll
